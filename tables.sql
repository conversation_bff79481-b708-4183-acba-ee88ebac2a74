CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    phone_number VARCHAR(20) NOT NULL UNIQUE,
    full_name NVARCHAR(255) NULL,
    total_spins INT UNSIGNED NOT NULL DEFAULT 0,
    used_spins INT UNSIGNED NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    reason_delete NVARCHAR(255) NULL,
    last_logged_in_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_call_ftel_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    has_received_spin_award BOOLEAN NOT NULL DEFAULT FALSE,
    has_shared_facebook BOOLEAN NOT NULL DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT chk_users_spins CHECK (total_spins >= used_spins)
);

CREATE INDEX idx_users_phone_number ON users(phone_number);

CREATE TABLE processed_orders (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    order_code VARCHAR(100) NOT NULL UNIQUE,
    contract_code VARCHAR(100) NOT NULL,
    camera_count INT UNSIGNED NOT NULL DEFAULT 0,
    service VARCHAR(100) DEFAULT NULL,
    full_name NVARCHAR(255) NULL,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_processed_orders_order_code ON processed_orders(order_code);

CREATE TABLE spin_award_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    processed_order_id BIGINT UNSIGNED NULL,
    spins_awarded INT UNSIGNED NOT NULL,
    reason NVARCHAR(255) NULL,
    award_source ENUM('PROCESSED_ORDER', 'FACEBOOK_SHARE', 'FIRST_LOGIN_AWARD', 'ADMIN_ADJUSTMENT', 'OTHER') NOT NULL DEFAULT 'OTHER',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_order_id) REFERENCES processed_orders(id) ON DELETE SET NULL
);

CREATE INDEX idx_spin_award_logs_user_id ON spin_award_logs(user_id);

CREATE TABLE products (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    product_image VARCHAR(4000) NOT NULL,
    product_name NVARCHAR(255) NOT NULL,
    product_type ENUM('VOUCHER', 'PHYSICAL_GIFT', 'GROUP_GIFT') NOT NULL DEFAULT 'VOUCHER', -- change,
    link_redirect VARCHAR(4000) NULL, -- change
    tutorial TEXT NULL, -- change
    description TEXT NULL,
    initial_quantity INT UNSIGNED NULL,
    remaining_quantity INT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);



CREATE TABLE product_instances (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    product_id INT UNSIGNED NOT NULL,
    instance_code VARCHAR(100) NOT NULL,
    status ENUM('AVAILABLE', 'RESERVED', 'USED') NOT NULL DEFAULT 'AVAILABLE',
    user_id BIGINT UNSIGNED NULL,
    start_at TIMESTAMP NULL, -- change
    end_at TIMESTAMP NULL,-- change
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

CREATE INDEX idx_product_instances_code ON product_instances(instance_code);
CREATE INDEX idx_product_instances_user_id ON product_instances(user_id);
CREATE INDEX idx_product_instances_status ON product_instances(status);

CREATE TABLE campaigns (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    campaign_name NVARCHAR(255) NOT NULL,
    description TEXT NULL,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT chk_campaigns_dates CHECK (start_date < end_date)
);

CREATE TABLE campaign_prizes (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    campaign_id INT UNSIGNED NOT NULL,
    product_id INT UNSIGNED NOT NULL,
    quantity_in_campaign INT UNSIGNED NOT NULL,
    prize_type ENUM('VOUCHER', 'PHYSICAL_GIFT', 'GROUP_GIFT') NOT NULL DEFAULT 'VOUCHER',
    group_products JSON DEFAULT NULL, -- Chứa danh sách sản phẩm con và trọng số cho GROUP_GIFT
    display_order INT UNSIGNED NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT uq_campaign_product UNIQUE (campaign_id, product_id),
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Bảng quản lý chi tiết group products trong campaign
CREATE TABLE campaign_prize_groups (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    campaign_prize_id BIGINT UNSIGNED NOT NULL,
    child_campaign_prize_id BIGINT UNSIGNED NOT NULL,
    quantity_weight INT UNSIGNED NOT NULL DEFAULT 1, -- Trọng số để random
    display_order INT UNSIGNED NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT uq_campaign_prize_child UNIQUE (campaign_prize_id, child_campaign_prize_id),
    FOREIGN KEY (campaign_prize_id) REFERENCES campaign_prizes(id) ON DELETE CASCADE,
    FOREIGN KEY (child_campaign_prize_id) REFERENCES campaign_prizes(id) ON DELETE CASCADE
);

CREATE INDEX idx_campaign_prizes_campaign_id ON campaign_prizes(campaign_id);
CREATE INDEX idx_campaign_prizes_type ON campaign_prizes(prize_type);
CREATE INDEX idx_campaign_prizes_active ON campaign_prizes(is_active);

CREATE INDEX idx_campaign_prize_groups_campaign_prize ON campaign_prize_groups(campaign_prize_id);
CREATE INDEX idx_campaign_prize_groups_child ON campaign_prize_groups(child_campaign_prize_id);
CREATE INDEX idx_campaign_prize_groups_active ON campaign_prize_groups(is_active);

CREATE TABLE spin_history (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    campaign_id INT UNSIGNED NOT NULL,
    campaign_prize_id BIGINT UNSIGNED NULL,
    product_instance_id BIGINT UNSIGNED NULL,
    actual_product_id INT UNSIGNED NULL, -- Product thực tế được trúng (dành cho GROUP_GIFT)
    campaign_prize_group_id BIGINT UNSIGNED NULL, -- Reference đến campaign_prize_groups nếu trúng GROUP_GIFT
    spun_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE,
    FOREIGN KEY (campaign_prize_id) REFERENCES campaign_prizes(id) ON DELETE SET NULL,
    FOREIGN KEY (product_instance_id) REFERENCES product_instances(id) ON DELETE SET NULL,
    FOREIGN KEY (actual_product_id) REFERENCES products(id) ON DELETE SET NULL,
    FOREIGN KEY (campaign_prize_group_id) REFERENCES campaign_prize_groups(id) ON DELETE SET NULL
);

CREATE INDEX idx_spin_history_user_id ON spin_history(user_id);
CREATE INDEX idx_spin_history_campaign_id ON spin_history(campaign_id);

CREATE TABLE settings (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description NVARCHAR(255) NULL
);


CREATE TABLE admins (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(500) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'admin',
    full_name NVARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

--------

ALTER TABLE products
MODIFY COLUMN product_type ENUM('VOUCHER', 'PHYSICAL_GIFT', 'GROUP_GIFT') NOT NULL DEFAULT 'VOUCHER',
ADD COLUMN link_redirect VARCHAR(4000) NULL,
ADD COLUMN tutorial TEXT NULL;

CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(255) NOT NULL,
    description VARCHAR(255) NULL
);

CREATE TABLE promotions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_name VARCHAR(255) NOT NULL,
    components TEXT NULL,
    sale_price DECIMAL(12, 0) NOT NULL,
    original_price DECIMAL(12, 0) NULL,
    cloud_storage VARCHAR(255) NULL,
    category_id INT,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

CREATE TABLE faq (
    id INT PRIMARY KEY AUTO_INCREMENT,
    question TEXT NOT NULL,
    answer TEXT NULL
);