include:
  - project: 'isc/cicd-config/ci-template/trunk-based-development'
    ref: feature/kimntt6
    file: 
      - '.gitlab/ci/nodejs/main.gitlab-ci.yml'

variables:
  GIT_STRATEGY: none
  GIT_CHECKOUT: "false"
  DOWNSTREAM__BRANCH: main
  PREFIX_TAG: 
    description: "Add prefix tag for build-image, to avoid to affect to a built image from a same commit. "
    value: ""
  USE_DEFAULT_BUILD_TEMPLATE:
    description: " "
    value: "true"
  UP_TO_DATE_APP_VERSION:
    description: " "
    value: "false"
  BUILD_AND_UNIT_TEST: ""
  DOWNSTREAM__URL: $GIT_PROTOCOL$GIT_HOST_URL
  DOWNSTREAM__A: $CI_SYS_GROUP_ACCESS_TOKEN
  DOWNSTREAM__P: $CI_TRIGGER_TOKEN
  DOWNSTREAM__PID: "13262"
