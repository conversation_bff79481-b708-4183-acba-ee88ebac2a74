// C<PERSON><PERSON> hình cho các thiết lập liên quan đến lượt quay
import { 
    SPINS_PER_CAMERA_ORDER as SPINS_ENV,
    FACEBOOK_SHARE_SPIN_AWARD as FB_AWARD_ENV,
    FIRST_LOGIN_SPIN_AWARD as FIRST_LOGIN_ENV,
    CAMPAIGN_BANNER_URL as BANNER_URL_ENV
} from './env.js';

// Số lượt quay thưởng cho mỗi camera trong đơn hàng
export const SPINS_PER_CAMERA_ORDER = SPINS_ENV;

// Số lượt quay thưởng khi chia sẻ lên Facebook
export const FACEBOOK_SHARE_SPIN_AWARD = FB_AWARD_ENV;

// Số lượt quay thưởng cho lần đăng nhập đầu tiên
export const FIRST_LOGIN_SPIN_AWARD = FIRST_LOGIN_ENV;



// URL ảnh banner cho chiến dịch hiện tại
export const CAMPAIGN_BANNER_URL = BANNER_URL_ENV;