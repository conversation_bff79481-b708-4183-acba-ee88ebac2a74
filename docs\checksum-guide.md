# Hướng dẫn sử dụng Middleware Checksum

## Giới thiệu

Middleware Checksum là một lớp bảo mật bổ sung cho API, gi<PERSON>p xác thực tính toàn vẹn và nguồn gốc của các request. Middleware này thực hiện các chức năng sau:

1. <PERSON><PERSON><PERSON> tra timestamp để đảm bảo request không quá cũ (không quá 10 phút)
2. Kiểm tra nonce để ngăn chặn tấn công replay (lưu trong Redis trong 10 phút)
3. <PERSON><PERSON><PERSON> thực checksum dựa trên path, query params, và body (nếu là POST/PUT/PATCH)
4. Sử dụng HMAC SHA-256 với secret key để tạo và xác thực checksum

## Cài đặt

### 1. Cấu hình biến môi trường

Thêm biến môi trường `API_SECRET_KEY` vào file `.env` của bạn:

```
API_SECRET_KEY=your-secret-key-change-in-production
```

### 2. Áp dụng middleware cho routes

Có hai cách để áp dụng middleware checksum:

#### Áp dụng cho toàn bộ router

```javascript
import express from 'express';
import { verifyChecksum } from '../middlewares/index.js';
import { API_SECRET_KEY } from '../configs/env.js';

const router = express.Router();

// Áp dụng middleware checksum cho tất cả các routes trong router này
router.use(verifyChecksum(API_SECRET_KEY));

// Các routes được bảo vệ bởi checksum
router.get('/secure-data', (req, res) => {
  // Xử lý request
});

export default router;
```

#### Áp dụng cho route cụ thể

```javascript
import express from 'express';
import { verifyChecksum } from '../middlewares/index.js';
import { API_SECRET_KEY } from '../configs/env.js';

const router = express.Router();

// Áp dụng middleware checksum cho route cụ thể
router.post('/secure-action', verifyChecksum(API_SECRET_KEY), (req, res) => {
  // Xử lý request
});

export default router;
```

## Hướng dẫn cho Client

Để gửi request hợp lệ đến API được bảo vệ bởi middleware checksum, client cần thực hiện các bước sau:

### 1. Tạo timestamp và nonce

```javascript
const timestamp = Date.now(); // Unix timestamp milliseconds
const nonce = `${timestamp}-${Math.random().toString(36).substring(2, 15)}`; // Tạo nonce ngẫu nhiên
```

### 2. Tạo chuỗi dữ liệu để tính checksum

#### Cho GET request

```javascript
let data = `timestamp=${timestamp}&nonce=${nonce}&path=/api/secure/secure-data`;

// Thêm query params nếu có
if (queryParams && Object.keys(queryParams).length > 0) {
  const params = new URLSearchParams();
  
  // Sắp xếp keys theo thứ tự a-z để đảm bảo tính nhất quán
  const sortedKeys = Object.keys(queryParams).sort();
  
  for (const key of sortedKeys) {
    if (queryParams[key] !== undefined && queryParams[key] !== null) {
      params.append(key, queryParams[key]);
    }
  }
  
  const queryString = params.toString();
  if (queryString) {
    data += `&${queryString}`;
  }
}
```

#### Cho POST/PUT/PATCH request

```javascript
let data = `timestamp=${timestamp}&nonce=${nonce}&path=/api/secure/secure-action`;

// Thêm query params nếu có
if (queryParams && Object.keys(queryParams).length > 0) {
  const params = new URLSearchParams();
  
  // Sắp xếp keys theo thứ tự a-z để đảm bảo tính nhất quán
  const sortedKeys = Object.keys(queryParams).sort();
  
  for (const key of sortedKeys) {
    if (queryParams[key] !== undefined && queryParams[key] !== null) {
      params.append(key, queryParams[key]);
    }
  }
  
  const queryString = params.toString();
  if (queryString) {
    data += `&${queryString}`;
  }
}

// Thêm body
if (body) {
  const bodyParams = new URLSearchParams();
  const sortedKeys = Object.keys(body).sort();
  
  for (const key of sortedKeys) {
    if (body[key] !== undefined && body[key] !== null) {
      const value = typeof body[key] === 'object' 
        ? JSON.stringify(body[key]) 
        : body[key].toString();
      
      bodyParams.append(key, value);
    }
  }
  
  const bodyString = bodyParams.toString();
  if (bodyString) {
    data += `&${bodyString}`;
  }
}
```

### 3. Tính toán HMAC SHA-256

```javascript
// Node.js
const crypto = require('crypto');
const hmac = crypto.createHmac('sha256', secretKey);
hmac.update(data);
const checksum = hmac.digest('hex');

// Browser (với crypto-js)
const hmac = CryptoJS.HmacSHA256(data, secretKey);
const checksum = hmac.toString(CryptoJS.enc.Hex);
```

### 4. Thêm headers vào request

```javascript
const headers = {
  'x-timestamp': timestamp,
  'x-nonce': nonce,
  'x-checksum': checksum
};
```

## Ví dụ đầy đủ

Xem file `src/utils/client-example.js` để xem ví dụ đầy đủ về cách tạo checksum từ phía client.

## Lưu ý bảo mật

1. **Secret Key**: Luôn giữ secret key an toàn và không bao giờ chia sẻ nó với client.
2. **Timestamp**: Kiểm tra timestamp giúp ngăn chặn tấn công replay lâu dài.
3. **Nonce**: Nonce đảm bảo mỗi request là duy nhất, ngăn chặn tấn công replay ngắn hạn.
4. **HTTPS**: Luôn sử dụng HTTPS để bảo vệ dữ liệu truyền tải.
5. **Logging**: Ghi log các lỗi xác thực để phát hiện các nỗ lực tấn công.

## Xử lý lỗi

Middleware checksum có thể trả về các lỗi sau:

- `CHECKSUM_REQUIRED`: Thiếu header x-checksum
- `TIMESTAMP_REQUIRED`: Thiếu header x-timestamp
- `NONCE_REQUIRED`: Thiếu header x-nonce
- `INVALID_TIMESTAMP`: Timestamp không hợp lệ
- `EXPIRED_REQUEST`: Request đã hết hạn (quá 10 phút)
- `DUPLICATE_NONCE`: Nonce đã được sử dụng
- `INVALID_CHECKSUM`: Checksum không hợp lệ

## Tùy chỉnh

Bạn có thể tùy chỉnh middleware checksum bằng cách:

1. Thay đổi thời gian hết hạn (hiện tại là 10 phút)
2. Thay đổi thuật toán mã hóa (hiện tại là HMAC SHA-256)
3. Thêm các trường bổ sung vào chuỗi dữ liệu để tính checksum