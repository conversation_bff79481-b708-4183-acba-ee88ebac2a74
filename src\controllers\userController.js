import { pool } from "../configs/db.js";

export const getMe = async (req, res, next) => {
  try {
    const userId = req.user?.id;

    const connection = await pool.getConnection();

    try {
      // L<PERSON>y thông tin người dùng
      const [rows] = await connection.query(
        "SELECT phone_number, full_name as fullname, total_spins, used_spins FROM users WHERE id = ? AND deleted_at IS NULL",
        [userId]
      );

      if (rows.length === 0) {
        return res.status(404).json({
          error: {
            code: "USER_NOT_FOUND",
            message: "<PERSON><PERSON><PERSON><PERSON> tìm thấy người dùng"
          }
        });
      }

      res.json(rows[0]);
    } finally {
      connection.release();
    }
  } catch (err) {
    next(err);
  }
}