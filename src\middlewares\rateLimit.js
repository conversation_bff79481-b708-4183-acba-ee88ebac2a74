import { rateLimit, ip<PERSON>eyGenerator } from 'express-rate-limit';
import { RedisStore } from 'rate-limit-redis';
import { redis } from '../configs/db.js';
import {
  NODE_ENV,
  RATE_LIMIT_WINDOW_MS,
  RATE_LIMIT_MAX_REQUESTS,
  LOGIN_RATE_LIMIT_WINDOW_MS,
  LOGIN_RATE_LIMIT_MAX_REQUESTS
} from '../configs/env.js';

// Cấu hình chung cho tất cả các API
const defaultRateLimiter = rateLimit({
  windowMs: RATE_LIMIT_WINDOW_MS, // Mặc định: 15 phút (900000ms)
  limit: RATE_LIMIT_MAX_REQUESTS, // Mặc định: 100 request
  standardHeaders: false, // Trả về thông tin rate limit trong header `RateLimit-*`
  legacyHeaders: false, // Tắt header `X-RateLimit-*`
  message: {
    status: 'error',
    code: 'TOO_MANY_REQUESTS',
    message: '<PERSON><PERSON><PERSON> nhiều yêu cầu, vui lòng thử lại sau.'
  },
  keyGenerator: (req) => {
    // Sử dụng IP address làm key cho rate limit
    return req.user?.id || ipKeyGenerator(req);
  },
  store: new RedisStore({
    // Sử dụng Redis instance từ db.js
    sendCommand: (...args) => redis.call(...args),
    prefix: 'rl:default:'
  }),
  skip: () => NODE_ENV === 'development' // Bỏ qua rate limit trong môi trường development
});

// Cấu hình riêng cho login API
const loginRateLimiter = rateLimit({
  windowMs: LOGIN_RATE_LIMIT_WINDOW_MS, // Mặc định: 1 giờ (3600000ms)
  limit: LOGIN_RATE_LIMIT_MAX_REQUESTS, // Mặc định: 5 request
  standardHeaders: false,
  legacyHeaders: false,
  message: {
    status: 'error',
    code: 'TOO_MANY_LOGIN_ATTEMPTS',
    message: 'Quá nhiều lần đăng nhập thất bại, vui lòng thử lại sau.'
  },
  store: new RedisStore({
    // Sử dụng Redis instance từ db.js
    sendCommand: (...args) => redis.call(...args),
    prefix: 'rl:login:'
  }),
  skip: () => NODE_ENV === 'development' // Bỏ qua rate limit trong môi trường development
});

export { defaultRateLimiter, loginRateLimiter };