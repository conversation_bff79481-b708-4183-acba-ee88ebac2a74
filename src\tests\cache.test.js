import { cacheMiddleware, clearCache } from '../middlewares/cache.js';
import { redis } from '../configs/db.js';

// Mock Redis
jest.mock('../configs/db.js', () => ({
  redis: {
    get: jest.fn(),
    set: jest.fn().mockResolvedValue('OK'),
    scan: jest.fn(),
    del: jest.fn().mockResolvedValue(1)
  }
}));

describe('Cache Middleware', () => {
  let req, res, next;

  beforeEach(() => {
    req = {
      originalUrl: '/v1/prizes',
      query: {}
    };
    res = {
      json: jest.fn()
    };
    next = jest.fn();
    jest.clearAllMocks();
  });

  test('should call next() when nocache=true', async () => {
    req.query.nocache = 'true';
    await cacheMiddleware()(req, res, next);
    expect(next).toHaveBeenCalled();
    expect(redis.get).not.toHaveBeenCalled();
  });

  test('should return cached data when available', async () => {
    const cachedData = { data: [{ id: 1, name: 'Prize 1' }] };
    redis.get.mockResolvedValue(JSON.stringify(cachedData));

    await cacheMiddleware()(req, res, next);

    expect(redis.get).toHaveBeenCalledWith('cache:/v1/prizes');
    expect(res.json).toHaveBeenCalledWith(cachedData);
    expect(next).not.toHaveBeenCalled();
  });

  test('should override res.json and call next() when no cache', async () => {
    redis.get.mockResolvedValue(null);
    const originalJson = res.json;

    await cacheMiddleware(60)(req, res, next);

    expect(redis.get).toHaveBeenCalledWith('cache:/v1/prizes');
    expect(next).toHaveBeenCalled();
    expect(res.json).not.toBe(originalJson);

    // Simulate calling the overridden res.json
    const data = { data: [{ id: 1, name: 'Prize 1' }] };
    res.json(data);

    expect(redis.set).toHaveBeenCalledWith(
      'cache:/v1/prizes',
      JSON.stringify(data),
      'EX',
      60
    );
  });

  test('should handle errors and continue', async () => {
    redis.get.mockRejectedValue(new Error('Redis error'));

    await cacheMiddleware()(req, res, next);

    expect(redis.get).toHaveBeenCalledWith('cache:/v1/prizes');
    expect(next).toHaveBeenCalled();
  });
});

describe('Clear Cache Middleware', () => {
  let req, res, next;

  beforeEach(() => {
    req = {};
    res = {};
    next = jest.fn();
    jest.clearAllMocks();
  });

  test('should clear cache with pattern and call next()', async () => {
    redis.scan.mockResolvedValueOnce(['0', ['cache:/v1/prizes', 'cache:/v1/prizes/detailed']]);

    await clearCache('cache:/v1/prizes*')(req, res, next);

    expect(redis.scan).toHaveBeenCalledWith('0', 'MATCH', 'cache:/v1/prizes*', 'COUNT', 100);
    expect(redis.del).toHaveBeenCalledWith('cache:/v1/prizes', 'cache:/v1/prizes/detailed');
    expect(next).toHaveBeenCalled();
  });

  test('should handle errors and continue', async () => {
    redis.scan.mockRejectedValue(new Error('Redis error'));

    await clearCache('cache:/v1/prizes*')(req, res, next);

    expect(redis.scan).toHaveBeenCalledWith('0', 'MATCH', 'cache:/v1/prizes*', 'COUNT', 100);
    expect(next).toHaveBeenCalled();
  });
});