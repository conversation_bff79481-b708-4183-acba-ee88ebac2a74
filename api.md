# Tài liệu API - Landing Page Vòng Quay May Mắn

Đây là tài liệu mô tả các API cho landing page vòng quay may mắn. Tài liệu này cung cấp thông tin về cách sử dụng các
endpoint, định dạng dữ liệu yêu cầu và phản hồi, cũng như các quy tắc xác thực và bảo mật.

## Base URL

```
Production: https://api-landing.fpt.vn
Staging: https://dev-api-landing.nvt16.site
```

## Xác thực

### Session-based Authentication
Các endpoint yêu cầu xác thực sử dụng session-based authentication thông qua OIDC (OpenID Connect). Các session được quản lý tự động sau khi đăng nhập thành công.

### Checksum Security
Tất cả các API endpoint (trừ `/callback`) yêu cầu xác thực checksum để đảm bảo tính toàn vẹn và bảo mật của request.

#### Headers yêu cầu:
- `x-checksum`: HMAC SHA-256 hex string
- `x-timestamp`: Unix timestamp milliseconds (phải trong vòng 10 phút)
- `x-nonce`: Chuỗi ngẫu nhiên duy nhất

#### Cách tính checksum:
1. Tạo chuỗi data theo format: `timestamp={timestamp}&nonce={nonce}&path={path}&{sorted_query_params}&{sorted_body_params}`
2. Sắp xếp query params và body params theo thứ tự a-z
3. Tính HMAC SHA-256 với secret key
4. Chuyển đổi thành hex string

## Rate Limiting

- **Mặc định**: 100 requests/15 phút
- **Login**: 5 requests/1 giờ
- Rate limit được áp dụng theo IP address hoặc user ID

## Mã trạng thái HTTP

API sử dụng các mã trạng thái HTTP tiêu chuẩn để chỉ ra thành công hay thất bại của một yêu cầu:

| Mã  | Mô tả                     |
|-----|---------------------------|
| 200 | Thành công                |
| 201 | Tạo thành công            |
| 400 | Yêu cầu không hợp lệ      |
| 401 | Chưa xác thực             |
| 403 | Không có quyền truy cập   |
| 404 | Không tìm thấy tài nguyên |
| 429 | Quá nhiều yêu cầu         |
| 500 | Lỗi máy chủ               |

## Định dạng lỗi

Khi xảy ra lỗi, API sẽ trả về một đối tượng JSON với cấu trúc sau:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Mô tả lỗi"
  }
}
```

### Các mã lỗi phổ biến:

#### Checksum & Security
- `BAD_REQUEST`: Thiếu header hoặc tham số không hợp lệ
- `INVALID_CHECKSUM`: Checksum không khớp

#### Authentication
- `NO_SESSION`: Không có session hợp lệ
- `SESSION_EXPIRED`: Session đã hết hạn

#### Rate Limiting
- `TOO_MANY_REQUESTS`: Quá nhiều yêu cầu
- `TOO_MANY_LOGIN_ATTEMPTS`: Quá nhiều lần đăng nhập thất bại

#### Business Logic
- `USER_NOT_FOUND`: Không tìm thấy người dùng
- `NO_SPINS_REMAINING`: Không còn lượt quay
- `NO_ACTIVE_CAMPAIGN`: Không có chiến dịch nào đang hoạt động
- `NO_PRIZE`: Không có giải thưởng
- `PRIZE_OUT_OF_STOCK`: Giải thưởng đã hết
- `ALREADY_RECEIVED`: Đã nhận thưởng
- `INVALID_TYPE`: Loại không hợp lệ

## Caching

Một số endpoint sử dụng Redis cache để tối ưu hiệu suất:
- Cache TTL: 60 giây cho các endpoint public
- Cache được tự động xóa khi có thay đổi dữ liệu
- Có thể bỏ qua cache bằng query parameter `nocache=true`

## Phân trang

Các endpoint trả về danh sách hỗ trợ phân trang với các tham số truy vấn sau:

| Tham số | Mô tả                           | Mặc định theo endpoint |
|---------|---------------------------------|------------------------|
| page    | Số trang (bắt đầu từ 1)         | 1                      |
| limit   | Số lượng kết quả trên mỗi trang | 10-12 (tùy endpoint)   |

### Giới hạn limit theo endpoint:
- `/v1/prizes`: 12
- `/v1/winners`: 10
- `/v1/users/me/rewards`: 10

Kết quả phân trang sẽ có định dạng:

```json
{
  "data": [
    ...
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "total_pages": 10
  }
}
```

## Danh sách Endpoint

### Tổng quan các endpoint:

| Endpoint | Method | Auth | Checksum | Rate Limit | Cache | Mô tả |
|----------|--------|------|----------|------------|-------|-------|
| `/callback` | GET | No | No | Default | No | OIDC callback |
| `/auth/login` | GET | No | Yes | Login | No | Tạo URL đăng nhập |
| `/auth/logout` | POST | Yes | Yes | Default | No | Đăng xuất |
| `/v1/spins` | POST | Yes | Yes | Default | No | Quay thưởng |
| `/v1/users/me` | GET | Yes | Yes | Default | No | Thông tin user |
| `/v1/users/me/rewards` | GET | Yes | Yes | Default | No | Lịch sử quà |
| `/v1/users/me/spins` | POST | Yes | Yes | Default | No | Cộng lượt quay |
| `/v1/prizes` | GET | Optional | Yes | No | 60s | Danh sách giải |
| `/v1/prizes/detailed` | GET | Optional | Yes | Default | 60s | Chi tiết giải |
| `/v1/winners` | GET | Optional | Yes | No | 60s | Người trúng |

---

## Chi tiết API Endpoints

### 1. Xác thực (Authentication)

#### GET /auth/login

Tạo URL xác thực OIDC để đăng nhập người dùng.

**Middleware:** `loginRateLimiter`, `verifyChecksum`

**Headers:**
- `x-checksum`: Required
- `x-timestamp`: Required
- `x-nonce`: Required

**Response:** `200 OK`
```json
{
  "success": true,
  "message": "Tạo URL xác thực thành công",
  "data": {
    "authorization_url": "https://accounts-stag.fpt.vn/auth/realms/fpt-accounts/protocol/openid-connect/auth?..."
  }
}
```

**Errors:**
- `400 BAD_REQUEST`: Thiếu header checksum
- `400 INVALID_CHECKSUM`: Checksum không hợp lệ
- `429 TOO_MANY_LOGIN_ATTEMPTS`: Quá nhiều lần đăng nhập (5 requests/1 giờ)

---

#### GET /callback

Xử lý callback từ OIDC provider sau khi người dùng xác thực.

**Middleware:** `defaultRateLimiter` (không có checksum)

**Query Parameters:**
- `code`: Authorization code từ OIDC provider
- `state`: State parameter để xác thực

**Response:** Redirect về frontend
- Success: `{FRONTEND_REDIRECT_URL}?login=success&phone_number=...&full_name=...&total_spins=...&used_spins=...`
- Error: `{FRONTEND_REDIRECT_URL}?login=error&error_code=...&error_message=...`

**Error Codes:**
- `SESSION_EXPIRED`: Session hết hạn hoặc không hợp lệ
- `MISSING_PARAMETERS`: Thiếu code hoặc state
- `STATE_MISMATCH`: State không khớp
- `CODE_INVALID`: Authorization code không hợp lệ

---

#### POST /auth/logout

Đăng xuất người dùng và xóa session.

**Middleware:** `clearSession`, `verifyChecksum`

**Authentication:** Required (session-based)

**Headers:**
- `x-checksum`: Required
- `x-timestamp`: Required
- `x-nonce`: Required

**Response:** `200 OK`
```json
{
  "success": true,
  "message": "Đăng xuất thành công"
}
```

**Errors:**
- `401 NO_SESSION`: Không có session hợp lệ
- `400 BAD_REQUEST`: Thiếu header checksum
- `400 INVALID_CHECKSUM`: Checksum không hợp lệ

### Người dùng

**Endpoint:** `/v1/users/me`  
**Method:** GET  
**Mô tả:** Lấy thông tin người dùng sau khi đăng nhập  
**Xác thực:** Yêu cầu  
**Phản hồi:** `200 OK`

```json
{
  "phone_number": "0987654321",
  "fullname": "Nguyễn Văn A",
  "total_spins": 100,
  "used_spins": 50
}
```

### Lượt quay

**Endpoint:** `/v1/spins`  
**Method:** POST  
**Mô tả:** Tạo lượt quay mới và nhận thưởng  
**Xác thực:** Yêu cầu  
**Phản hồi:** `200 OK`

```json
{
  "product_id": 1,
  "actual_product_id": 2,
  "instance_code": "ABC123",
  "product_name": "Ipad",
  "product_image": "https://example.com/image.jpg"
}
```

### Người trúng thưởng

**Endpoint:** `/v1/winners`  
**Method:** GET  
**Mô tả:** Lấy danh sách người trúng thưởng  
**Xác thực:** Không yêu cầu  
**Query params:** page, limit  
**Phản hồi:** `200 OK`

```json
{
  "data": [
    {
      "phone_number": "098xxx321",
      "full_name": "Nguyễn Văn A",
      "product": {
        "name": "Ipad",
        "image": "https://example.com/image.jpg"
      },
      "spun_at": "2023-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "total_pages": 10
  }
}
```

### Lịch sử quà

**Endpoint:** `/v1/users/me/rewards`  
**Method:** GET  
**Mô tả:** Lấy lịch sử quà của người dùng  
**Xác thực:** Yêu cầu  
**Query params:** page, limit  
**Phản hồi:** `200 OK`

```json
{
  "data": [
    {
      "spun_at": "2023-01-01T00:00:00Z",
      "product_instance": {
        "code": "GIFT123"
      },
      "product": {
        "name": "Ipad",
        "image": "https://example.com/image.jpg",
        "product_type": "PHYSICAL"
      }
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "total_pages": 10
  }
}
```

### Lượt quay bổ sung

**Endpoint:** `/v1/users/me/spins`  
**Method:** POST  
**Mô tả:** Cộng lượt quay cho người dùng  
**Xác thực:** Yêu cầu  
**Request Body:**

```json
{
  "type": "facebook"
}
```

**Phản hồi:** `201 Created`

```json
{
  "message": "Cộng lượt quay thành công",
  "total_spin": 101
}
```

### Giải thưởng

**Endpoint:** `/v1/prizes`  
**Method:** GET  
**Mô tả:** Lấy danh sách giải thưởng có sẵn  
**Xác thực:** Không yêu cầu  
**Query params:** page, limit  
**Phản hồi:** `200 OK`

```json
{
  "data": [
    {
      "id": 1,
      "name": "Ipad",
      "image": "https://example.com/image.jpg",
      "type": "PHYSICAL",
      "quantity": 10,
      "quantity_in_campaign": 5,
      "display_order": 1,
      "is_active": 1
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "total_pages": 10
  },
  "campaign_id": 1
}
```

**Endpoint:** `/v1/prizes/detailed`  
**Method:** GET  
**Mô tả:** Lấy danh sách giải thưởng chi tiết với thông tin số lượng  
**Xác thực:** Không yêu cầu  
**Phản hồi:** `200 OK`

```json
{
  "data": [
    {
      "id": 1,
      "product_name": "Ipad",
      "product_image": "https://example.com/image.jpg",
      "product_type": "PHYSICAL",
      "total_quantity": 20,
      "remaining_quantity": 15,
      "used_quantity": 5
    }
  ],
  "total": 10
}
