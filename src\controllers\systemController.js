import { getKafkaStatus } from '../utils/kafkaInit.js';

/**
 * <PERSON><PERSON><PERSON> tra trạng thái hệ thống
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const getSystemStatus = async (req, res, next) => {
  try {
    // Lấy thông tin trạng thái Kafka
    const kafkaStatus = getKafkaStatus();
    
    // Trả về thông tin trạng thái hệ thống
    return res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      kafka: kafkaStatus
    });
  } catch (error) {
    next(error);
  }
};