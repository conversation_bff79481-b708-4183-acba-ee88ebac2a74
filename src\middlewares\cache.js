import { redis } from '../configs/db.js';
import { REDIS_CACHE_ENABLED, REDIS_CACHE_TTL, NODE_ENV } from '../configs/env.js';

/**
 * Middleware để cache response trong Redis
 * Chỉ cache các response có status code 200 (OK)
 * Điều này đảm bảo rằng các response lỗi (như 400, 500) không được cache
 * @param {number} duration - Thời gian cache tính bằng giây
 * @returns {function} - Express middleware
 */
export const cacheMiddleware = (duration = REDIS_CACHE_TTL) => {
  return async (req, res, next) => {
    // Bỏ qua cache nếu cache bị tắt, đang ở môi trường development, hoặc có query param nocache=true
    if (!REDIS_CACHE_ENABLED || req.query.nocache === 'true') {
      return next();
    }

    // Tạo cache key từ đường dẫn và query params
    const cacheKey = `cache:${req.originalUrl || req.url}`;

    try {
      // Kiểm tra xem dữ liệu đã được cache chưa
      const cachedData = await redis.get(cacheKey);

      if (cachedData) {
        // Nếu có dữ liệu cache, đặt status code là 200 và trả về ngay
        res.status(200);
        const parsedData = JSON.parse(cachedData);
        return res.json(parsedData);
      }

      // Nếu không có cache, ghi đè phương thức res.json để lưu response vào cache
      const originalJson = res.json;
      res.json = function(data) {
        // Gọi phương thức json gốc trước để đảm bảo response được gửi
        const result = originalJson.call(this, data);
        
        // Chỉ lưu cache khi status code là 200
        // Kiểm tra this.statusCode sau khi gọi originalJson vì một số middleware có thể đã thay đổi status code
        if (this.statusCode === 200 || this.statusCode === undefined) {
          // Lưu dữ liệu vào cache
          redis.set(cacheKey, JSON.stringify(data), 'EX', duration)
            .catch(err => console.error('Redis cache error:', err));
        }

        return result;
      };

      // Tiếp tục xử lý request
      next();
    } catch (error) {
      console.error('Cache middleware error:', error);
      next(); // Tiếp tục xử lý request ngay cả khi có lỗi cache
    }
  };
};

/**
 * Middleware để xóa cache theo pattern
 * @param {string} pattern - Pattern của cache key cần xóa
 * @returns {function} - Express middleware
 */
export const clearCache = (pattern) => {
  return async (req, res, next) => {
    try {
      // Sử dụng SCAN để tìm tất cả các key theo pattern
      let cursor = '0';
      do {
        const [nextCursor, keys] = await redis.scan(cursor, 'MATCH', pattern, 'COUNT', 100);
        cursor = nextCursor;
        
        if (keys.length > 0) {
          await redis.del(...keys);
        }
      } while (cursor !== '0');

      next();
    } catch (error) {
      console.error('Clear cache error:', error);
      next();
    }
  };
};