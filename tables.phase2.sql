ALTER TABLE products
MODIFY COLUMN product_type ENUM('VOUCHER', 'PHYSICAL_GIFT', 'GROUP_GIFT') NOT NULL DEFAULT 'VOUCHER',
ADD COLUMN link_redirect VARCHAR(4000) NULL,
ADD COLUMN tutorial TEXT NULL;

CREATE TABLE Categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(255) NOT NULL,
    description VARCHAR(255) NULL
);

CREATE TABLE Products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_name VARCHAR(255) NOT NULL,
    components TEXT NULL,
    sale_price DECIMAL(12, 0) NOT NULL,
    original_price DECIMAL(12, 0) NULL,
    cloud_storage VARCHAR(255) NULL,
    category_id INT,
    FOREIGN KEY (category_id) REFERENCES Categories(id)
);

CREATE TABLE FAQ (
    id INT PRIMARY KEY AUTO_INCREMENT,
    question TEXT NOT NULL,
    answer TEXT NULL
);