import { pool } from "../configs/db.js";

export const getRewards = async (req, res, next) => {
  try {
    const userId = req.user?.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const connection = await pool.getConnection();

    try {
      // Đếm tổng số records
      const [countResult] = await connection.query(
        `SELECT COUNT(*) as total 
         FROM spin_history sh
         INNER JOIN product_instances pi ON sh.product_instance_id = pi.id
         INNER JOIN products p ON pi.product_id = p.id
         WHERE sh.user_id = ? AND pi.id IS NOT NULL`,
        [userId]
      );

      const total = countResult[0].total;
      const totalPages = Math.ceil(total / limit);

      // Lấy dữ liệu với phân trang
      const [rows] = await connection.query(
        `SELECT 
           sh.spun_at,
           pi.instance_code as code,
           p.product_name as name,
           p.product_image as image,
           p.product_type as type,
           p.link_redirect,
           p.tutorial
         FROM spin_history sh
         INNER JOIN product_instances pi ON sh.product_instance_id = pi.id
         INNER JOIN products p ON pi.product_id = p.id
         WHERE sh.user_id = ? AND pi.id IS NOT NULL
         ORDER BY sh.spun_at DESC
         LIMIT ? OFFSET ?`,
        [userId, limit, offset]
      );

      // Format dữ liệu theo API specification
      const data = rows.map(row => ({
        spun_at: row.spun_at,
        product_instance: {
          code: row.code
        },
        product: {
          name: row.name,
          type: row.type,
          image: row.image,
          link_redirect: row.link_redirect,
          tutorial: row.tutorial
        }
      }));

      res.json({
        data,
        pagination: {
          total,
          page,
          limit,
          total_pages: totalPages
        }
      });
    } finally {
      connection.release();
    }
  } catch (err) {
    next(err);
  }
}