# Tài Liệu Mô Tả Kỹ Thuật: Landing Page & Minigame Vòng Quay May Mắn

## 1. Giới thiệu

Tài liệu này cung cấp mô tả chi tiết về yêu cầu chức năng, quy trình nghiệp vụ và đặc tả kỹ thuật cho dự án "Landing Page & Minigame Vòng Quay May Mắn". Tài liệu là cơ sở để các bên liên quan (Development, QA/Tester, FPT/CSOC) thực hiện phát triển, ki<PERSON><PERSON> thử, và đánh giá bảo mật.

### 1.1 Mục tiêu dự án

- Xây dựng landing page quảng bá sản phẩm camera của FPT Telecom
- Tích hợp minigame vòng quay may mắn để tăng tương tác với người dùng
- Tạ<PERSON> c<PERSON> chế khuyến khích người dùng mua sản phẩm thông qua phần thưởng hấp dẫn

### 1.2 Phạm vi dự án

- Phát triển landing page với thông tin sản phẩm camera
- Xây dựng hệ thống xác thực người dùng thông qua FPT ID
- Phát triển minigame vòng quay may mắn với cơ chế phần thưởng
- Tích hợp với hệ thống FPT Telecom để kiểm tra thông tin mua hàng

## 2. Đặc tả kỹ thuật

### 2.1 Kiến trúc hệ thống

Hệ thống được xây dựng theo mô hình client-server với các thành phần chính:

- **Frontend**: Giao diện người dùng, hiển thị landing page và minigame vòng quay may mắn
- **Backend API**: Xử lý các yêu cầu từ frontend, tương tác với database và các dịch vụ bên ngoài
- **Database**: Lưu trữ dữ liệu người dùng, phần thưởng, lịch sử quay số
- **Redis**: cache dữ liệu truy vấn từ database và lưu session để xác thực người dùng

### 2.2 Công nghệ sử dụng

#### 2.2.1 Frontend

- **Framework**: Nuxt.js (dựa trên Vue.js)
- **Ngôn ngữ**: JavaScript/TypeScript
- **Thư viện UI**: không có

#### 2.2.2 Backend

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MySQL
- **Cache & Queue**: Redis
- **Thư viện chính**:
  - `axios`: Gọi API HTTP
  - `compression`: Nén response
  - `connect-redis`: Kết nối Redis với session
  - `cors`: Quản lý Cross-Origin Resource Sharing
  - `express`: Framework web
  - `express-rate-limit`: Giới hạn tốc độ request
  - `express-session`: Quản lý session
  - `express-validator`: Validate dữ liệu đầu vào
  - `helmet`: Bảo mật HTTP headers
  - `ioredis`: Tương tác với Redis
  - `mysql2`: Kết nối và truy vấn database
  - `openid-client`: Xác thực OpenID Connect
  - `winston`: Logging

### 2.3 ERD

```mermaid
erDiagram
    users {
        BIGINT id PK
        VARCHAR(20) phone_number UK "Định danh duy nhất"
        NVARCHAR(255) full_name
        INT_UNSIGNED total_spins
        INT_UNSIGNED used_spins
        TIMESTAMP created_at
        TIMESTAMP deleted_at "NULL nếu chưa bị xóa"
        NVARCHAR(255) reason_delete
        TIMESTAMP last_logged_in_at
        TIMESTAMP last_call_ftel_at
        BOOLEAN has_received_spin_award
        BOOLEAN has_shared_facebook
        TIMESTAMP updated_at
    }
    processed_orders {
        BIGINT id PK
        BIGINT user_id FK
        VARCHAR(100) order_code UK "Chống cộng trùng lượt"
        VARCHAR(100) contract_code
        INT_UNSIGNED camera_count
        VARCHAR(100) service
        NVARCHAR(255) full_name
        TIMESTAMP processed_at
    }
    spin_award_logs {
        BIGINT id PK
        BIGINT user_id FK
        BIGINT processed_order_id FK
        INT_UNSIGNED spins_awarded
        NVARCHAR(255) reason
        ENUM award_source "PROCESSED_ORDER/FACEBOOK_SHARE/FIRST_LOGIN_AWARD/ADMIN_ADJUSTMENT/OTHER"
        TIMESTAMP created_at
    }
    campaigns {
        INT id PK
        NVARCHAR(255) campaign_name
        TEXT description
        TIMESTAMP start_date
        TIMESTAMP end_date
        BOOLEAN is_active
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }
    products {
        INT id PK
        VARCHAR(4000) product_image
        NVARCHAR(255) product_name
        ENUM product_type "VOUCHER/PHYSICAL_GIFT"
        TEXT description
        INT_UNSIGNED initial_quantity
        INT_UNSIGNED remaining_quantity
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }
    campaign_prizes {
        BIGINT id PK
        INT campaign_id FK
        INT product_id FK
        INT_UNSIGNED quantity_in_campaign
        ENUM prize_type "VOUCHER/PHYSICAL_GIFT/GROUP_GIFT"
        JSON group_products "Danh sách sản phẩm con cho GROUP_GIFT"
        INT_UNSIGNED display_order
        BOOLEAN is_active
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }
    campaign_prize_groups {
        BIGINT id PK
        BIGINT campaign_prize_id FK
        BIGINT child_campaign_prize_id FK
        INT_UNSIGNED quantity_weight "Trọng số random"
        INT_UNSIGNED display_order
        BOOLEAN is_active
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }
    product_instances {
        BIGINT id PK
        INT product_id FK
        VARCHAR(100) instance_code "Mã voucher/quà cụ thể"
        ENUM status "AVAILABLE/RESERVED/USED"
        BIGINT user_id FK "User sở hữu"
        DATETIME start_at
        DATETIME end_at
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }
    spin_history {
        BIGINT id PK
        BIGINT user_id FK
        INT campaign_id FK
        BIGINT campaign_prize_id FK "Giải đã trúng"
        BIGINT product_instance_id FK "Mã voucher đã trúng"
        INT actual_product_id FK "Product thực tế cho GROUP_GIFT"
        BIGINT campaign_prize_group_id FK "Reference GROUP_GIFT"
        TIMESTAMP spun_at
    }
    settings {
        VARCHAR(100) setting_key PK
        TEXT setting_value
        NVARCHAR(255) description
    }
    admins {
        INT id PK
        VARCHAR(100) username UK
        VARCHAR(500) password
        VARCHAR(50) role
        NVARCHAR(255) full_name
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }
    
    users ||--o{ processed_orders : "sở hữu"
    users ||--o{ spin_history : "thực hiện"
    users ||--o{ product_instances : "sở hữu giải"
    users ||--o{ spin_award_logs : "nhận lượt quay"

    processed_orders ||--o{ spin_award_logs : "tạo lượt quay"

    campaigns ||--o{ campaign_prizes : "cấu hình giải"
    campaigns ||--o{ spin_history : "thuộc về"
    
    products ||--o{ campaign_prizes : "là một giải"
    products ||--o{ product_instances : "có các mã cụ thể"
    products ||--o{ spin_history : "actual_product"

    campaign_prizes ||--o{ campaign_prize_groups : "parent prize"
    campaign_prizes ||--o{ campaign_prize_groups : "child prize"
    campaign_prizes ||--o{ spin_history : "giải trúng"
    
    campaign_prize_groups ||--o{ spin_history : "GROUP_GIFT reference"
    
    product_instances ||--o{ spin_history : "voucher trúng"
```

## 3. Mô tả tính năng

### 3.1 Flow đăng nhập và nhận lượt quay

Flow đăng nhập và nhận lượt quay được thực hiện theo các bước sau:

1. **Đăng nhập qua FPT ID**:
   - Người dùng nhấn button đăng nhập trên giao diện landing page
   - Hệ thống chuyển hướng sang FPT ID để người dùng đăng nhập bằng số điện thoại
   - Sau khi đăng nhập thành công, FPT ID redirect về landing page kèm id token
   - Backend xác thực id token và trích xuất thông tin người dùng (số điện thoại, họ tên)
   - Hệ thống lưu thông tin người dùng vào database nếu chưa tồn tại

2. **Nhận lượt quay từ mua sản phẩm**:
   - Backend sử dụng số điện thoại của người dùng để gọi API sang FPT Telecom
   - Hệ thống kiểm tra số lượng camera đã mua trong thời gian diễn ra sự kiện
   - Mỗi camera mua được tính 3 lượt quay
   - Hệ thống cập nhật số lượt quay (total_spins) cho người dùng
   - Để tránh cộng trùng lượt, hệ thống lưu lại mã đơn hàng đã xử lý trong bảng processed_orders

3. **Nhận lượt quay khi đăng nhập lần đầu**:
   - Người dùng đăng nhập vào landing page lần đầu tiên **VÀ có đơn hàng camera** sẽ nhận được 1 lượt quay thưởng.
   - Điều kiện: `!user.has_received_spin_award` và `totalNewSpins > 0` (có đơn hàng camera mới).
   - Hệ thống tăng `total_spins` theo `FIRST_LOGIN_SPIN_AWARD`, cập nhật `has_received_spin_award` thành `TRUE` và ghi nhận vào `spin_award_logs` với `award_source` là `FIRST_LOGIN_AWARD`.
   - **Lưu ý**: Chỉ nhận thưởng đăng nhập lần đầu khi có mua camera, không phải chỉ đăng nhập.

4. **Nhận lượt quay từ chia sẻ Facebook**:
   - Người dùng nhấn nút chia sẻ landing page lên Facebook.
   - Frontend gửi POST request đến `/v1/users/me/spins` với `{"type": "facebook"}`.
   - Hệ thống kiểm tra trường `has_shared_facebook` trong bảng `users` để đảm bảo chỉ nhận 1 lần duy nhất.
   - Khi đủ điều kiện, hệ thống tăng `total_spins` theo `FACEBOOK_SHARE_SPIN_AWARD`, cập nhật `has_shared_facebook` thành `TRUE` và ghi nhận vào `spin_award_logs` với `award_source` là `FACEBOOK_SHARE`.
   - Sử dụng database transaction để đảm bảo tính nhất quán dữ liệu.

### 3.2 Flow quay vòng quay may mắn

Flow quay vòng quay may mắn được thực hiện theo các bước sau:

1. **Phía người dùng (Frontend)**:
   - Người dùng nhấn nút quay số trên giao diện vòng quay may mắn
   - Hệ thống yêu cầu người dùng xác thực captcha
   - Frontend gửi API request kèm captcha token và session cookie của người dùng

2. **Xử lý request (Backend)**:
   - Backend kiểm tra tính hợp lệ của captcha token
   - Nếu captcha không hợp lệ, trả về lỗi và yêu cầu xác thực lại
   - Nếu captcha hợp lệ, tiếp tục kiểm tra session để xác thực người dùng
   - Nếu session không hợp lệ hoặc hết hạn, trả về lỗi xác thực

3. **Xử lý quay số (Synchronous)**:
   - Khởi tạo transaction MySQL
   - Lock row user trong database để đảm bảo tính nhất quán (FOR UPDATE)
   - Kiểm tra số lượt quay còn lại của user (total_spins - used_spins)
   - Nếu hết lượt quay, rollback transaction và trả về lỗi
   - Nếu còn lượt quay, tăng used_spins lên 1 và cập nhật thông tin user

4. **Xác định campaign và phần thưởng**:
   - Lấy campaign hiện tại đang hoạt động (is_active = 1 và trong khoảng thời gian start_date - end_date)
   - Lấy danh sách phần thưởng từ bảng campaign_prizes với điều kiện:
     - Thuộc campaign hiện tại
     - quantity_in_campaign > 0
     - is_active = 1
     - Sắp xếp theo display_order và id
   - Tính tổng số lượng giải thưởng (totalQuantity)
   - Chọn số ngẫu nhiên từ 1 đến totalQuantity
   - Duyệt danh sách để tìm giải thưởng trúng dựa trên khoảng quantity_in_campaign
   - Trừ quantity_in_campaign của giải thưởng được chọn đi 1

5. **Xử lý GROUP_GIFT (nếu có)**:
   - Nếu prize_type = 'GROUP_GIFT', lấy danh sách sản phẩm con từ campaign_prize_groups
   - Tính tổng trọng số (quantity_weight) của các sản phẩm con
   - Random dựa trên trọng số để chọn sản phẩm con thực tế
   - Cập nhật actualProductId và campaignPrizeGroupId
   - Kiểm tra xem sản phẩm có thuộc group không để xác định returnProductId

6. **Cấp phát phần thưởng**:
   - Tìm product_instance có sẵn với status = "AVAILABLE" cho actualProductId
   - Nếu không có instance khả dụng, rollback transaction và trả về lỗi
   - Cập nhật trạng thái product_instance thành "USED" và gán user_id
   - Tạo lịch sử quay trong bảng spin_history với đầy đủ thông tin:
     - user_id, campaign_id, campaign_prize_id
     - product_instance_id, actual_product_id, campaign_prize_group_id
   - Commit transaction để hoàn tất quá trình

7. **Trả kết quả**:
   - Lấy thông tin chi tiết sản phẩm trúng thực tế (product_name, product_image)
   - Trả về response với:
     - product_id: ID của nhóm (nếu có) hoặc sản phẩm gốc
     - actual_product_id: ID của sản phẩm trúng thực tế
     - instance_code: Mã sản phẩm cụ thể
     - product_name: Tên sản phẩm trúng
     - product_image: Ảnh sản phẩm trúng

```mermaid
graph LR
    subgraph "1: Xác thực và Kiểm tra"
        direction LR
        A[Người dùng] --> B[Gọi API]
        B --> C{Xác thực OK?}
        C -->|Không| D[Lỗi]
        C -->|Có| E[Bắt đầu TX]
        E --> F{Còn lượt quay?}
        F -->|Không| G[Rollback]
        F -->|Có| H[Cập nhật lượt quay]
    end
    
    subgraph "2: Xử lý giải thưởng"
        direction LR
        I[Lấy Campaign] --> J[Chọn giải]
        J --> K{GROUP_GIFT?}
        K -->|Có| L[Random sản phẩm con]
        K -->|Không| M[Tìm Instance]
        L --> M
        M --> N{Instance OK?}
        N -->|Không| O[Rollback]
        N -->|Có| P[Cập nhật Instance]
        P --> Q[Lưu lịch sử]
        Q --> R[Commit]
        R --> S[Trả kết quả]
    end
    
    H --> I

```

## 4. Quy trình nghiệp vụ

### 4.1 Quy trình đăng nhập và nhận lượt quay

- Mỗi người dùng đăng nhập bằng FPT ID (số điện thoại) sẽ được hệ thống xác thực
- Hệ thống sẽ kiểm tra số lượng camera đã mua trong thời gian diễn ra sự kiện thông qua API của FTel
- **Đăng nhập lần đầu**: 1 lượt quay duy nhất trong suốt vòng đời tài khoản.
- **Chia sẻ Facebook**: 2 lượt quay duy nhất trong suốt vòng đời tài khoản.
- **Phát sinh đơn hàng**: 3 lượt quay cho mỗi camera hợp lệ.
- **Admin cộng tay**: Admin có thể cộng lượt quay cho người dùng.

### 4.2 Quy trình quay số và nhận thưởng

- Người dùng phải có ít nhất 1 lượt quay (total_spins > used_spins)
- Mỗi lần quay sẽ tiêu thụ 1 lượt quay
- Hệ thống xác định phần thưởng dựa trên tỉ lệ trúng và số lượng giải còn lại
- Kết quả quay số được lưu vào lịch sử và người dùng được cấp phát phần thưởng tương ứng

## 5. Bảo mật và chống spam

### 5.1 Biện pháp bảo mật

- **Xác thực người dùng**:
  - Sử dụng session-based authentication với session được lưu trữ trên redis
  - Session có thời hạn sử dụng giới hạn và được quản lý tự động
  - Mỗi session được liên kết với user_id và có thể thu hồi khi cần thiết

- **Bảo vệ API**:
  - Sử dụng CORS (Cross-Origin Resource Sharing) để kiểm soát truy cập từ các domain khác
  - Validate dữ liệu đầu vào cho tất cả các API

- **Bảo vệ dữ liệu**:
  - Sử dụng prepared statements để ngăn chặn SQL Injection
  - Mã hóa dữ liệu nhạy cảm trong database
  - Kiểm soát quyền truy cập database

### 5.2 Biện pháp chống spam và gian lận

- **Rate limiting**:
  - Áp dụng rate limit cho từng API theo địa chỉ IP
  - Giới hạn số lượng request trong một khoảng thời gian nhất định
  - Cấu hình rate limit khác nhau cho từng loại API

- **Captcha**:
  - Áp dụng captcha cho các API quan trọng:
    - API quay vòng quay may mắn
    - API nhận lượt quay hằng ngày
    - API chia sẻ Facebook

- **Giới hạn nhận thưởng**:
  - API đăng nhập lần đầu chỉ được cộng lượt quay 1 lần duy nhất trong suốt vòng đời tài khoản.
  - API chia sẻ Facebook chỉ được cộng lượt quay 1 lần duy nhất trong suốt vòng đời tài khoản.
  - Kiểm tra và lưu trữ lịch sử nhận thưởng trong bảng `users` (trường `has_received_spin_award` và `has_shared_facebook`) và `spin_award_logs`.

- **Xử lý giao dịch**:
  - Sử dụng transaction và row locking để đảm bảo tính nhất quán của dữ liệu
  - Kiểm tra số lượng giải thưởng còn lại trước khi cấp phát
  - Ghi nhận đầy đủ lịch sử quay số và nhận thưởng
