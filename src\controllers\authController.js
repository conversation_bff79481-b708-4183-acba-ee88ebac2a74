import * as client from 'openid-client';
import { OIDC_CONFIG, getOIDCConfig } from '../configs/oidc.js';
import { pool } from '../configs/db.js';
import { getContractByPhone } from '../utils/ftel.js';
import { SPINS_PER_CAMERA_ORDER, FIRST_LOGIN_SPIN_AWARD } from '../configs/settings.js';

export const login = async (req, res, next) => {
  try {
    const config = getOIDCConfig();

    // Tạo PKCE code verifier cho mỗi request và lưu vào session
    req.session.code_verifier = client.randomPKCECodeVerifier();
    // Tính toán code challenge từ code verifier (bất đồng bộ)
    const code_challenge = await client.calculatePKCECodeChallenge(req.session.code_verifier);

    // Luôn tạo state để đảm bảo an toàn và tương thích với server
    req.session.state = client.randomState();

    // Tạo parameters cho URL xác thực
    const parameters = {
      redirect_uri: OIDC_CONFIG.REDIRECT_URI,
      scope: OIDC_CONFIG.SCOPE,
      code_challenge,
      code_challenge_method: 'S256',
      state: req.session.state // Luôn bao gồm state parameter
    };

    // Tạo URL xác thực
    const redirectTo = client.buildAuthorizationUrl(config, parameters);

    console.log('Redirecting to authorization URL:', redirectTo.href);

    res.json({
      success: true,
      message: 'Tạo URL xác thực thành công',
      data: {
        authorization_url: redirectTo.href
      }
    });

  } catch (err) {
    console.error('Login Error:', err);
    next(err);
  }
}

export const callback = async (req, res, next) => {
  try {
    const config = getOIDCConfig();

    // Kiểm tra xem session có chứa code_verifier và state không
    if (!req.session.code_verifier || !req.session.state) {
      console.error('Session expired or invalid.', req.session);
      return res.status(400).json({
        success: false,
        message: 'Phiên đăng nhập đã hết hạn hoặc không hợp lệ. Vui lòng đăng nhập lại.',
        error_code: 'SESSION_EXPIRED'
      });
    }

    // Lấy code và state từ query params
    const code = req.query.code;
    const state = req.query.state;

    // Kiểm tra xem code và state có tồn tại không
    if (!code || !state) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu mã xác thực hoặc tham số state.',
        error_code: 'MISSING_PARAMETERS'
      });
    }

    // Tạo URL đầy đủ cho callback để sử dụng với authorizationCodeGrant
    const protocol = req.secure ? 'https' : 'http';
    // Lấy OIDC_REDIRECT_URI và loại bỏ phần /callback ở cuối nếu có
    const baseUrl = OIDC_CONFIG.REDIRECT_URI.endsWith('/callback') 
      ? OIDC_CONFIG.REDIRECT_URI.slice(0, -9) 
      : OIDC_CONFIG.REDIRECT_URI.replace(/\/callback$/, '');
    const fullUrl = new URL(`${baseUrl}${req.originalUrl}`);
    console.log('Full URL:', fullUrl.href);

    // Xác thực callback và trao đổi mã authorization code
    const tokens = await client.authorizationCodeGrant(
      config,
      fullUrl,
      {
        code: code,
        redirect_uri: OIDC_CONFIG.REDIRECT_URI, // Đảm bảo redirect_uri khớp với request authorize
        pkceCodeVerifier: req.session.code_verifier, // Sử dụng code_verifier từ session
        expectedState: req.session.state, // Sử dụng state từ session
      }
    );

    console.log('Received tokens:', tokens);
    console.log('Access Token:', tokens.access_token);
    console.log('ID Token:', tokens.id_token);
    console.log('Expires In:', tokens.expires_in);

    // Trích xuất claims từ ID token
    const claims = tokens.claims();
    console.log('ID Token Claims:', claims);
    const { sub } = claims;

    // Lấy thông tin người dùng từ UserInfo Endpoint với expectedSubject
    const userinfoResponse = await client.fetchUserInfo(
      config,
      tokens.access_token,
      sub
    );

    const userinfo = userinfoResponse;

    delete req.session.code_verifier;
    delete req.session.state;

    // Kiểm tra và tạo/cập nhật user
    let [users] = await pool.query('SELECT * FROM users WHERE phone_number = ?', [userinfo.phone_number]);
    let user = users[0];
    let userId;

    // Gọi API FTel để lấy thông tin đơn hàng và fullname
    let ftelFullName = userinfo.name || userinfo.preferred_username;
    try {
      const ftelResponse = await getContractByPhone(userinfo.phone_number);
      if (ftelResponse.Code === 200 && ftelResponse.Data && ftelResponse.Data.Order && ftelResponse.Data.Order.length > 0) {
        // Lấy fullname từ order đầu tiên
        ftelFullName = ftelResponse.Data.Order[0].FullName || ftelFullName;
      }
    } catch (ftelError) {
      console.error('FTel API Error when getting fullname:', ftelError);
    }

    if (!user) {
      // Tạo user mới
      const [result] = await pool.query(
        'INSERT INTO users (phone_number, full_name, last_logged_in_at, last_call_ftel_at) VALUES (?, ?, NOW(), NOW())',
        [userinfo.phone_number, ftelFullName]
      );
      userId = result.insertId;
    } else {
      // Cập nhật thời gian đăng nhập và fullname
      userId = user.id;
      await pool.query(
        'UPDATE users SET last_logged_in_at = NOW(), full_name = ? WHERE id = ?',
        [ftelFullName, userId]
      );
    }

    // Lấy thông tin user sau khi tạo/cập nhật
    [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
    user = users[0];

    // Lưu thông tin user vào session
    req.session.user = {
      id: user.id,
      phone_number: user.phone_number,
      full_name: user.full_name,
      total_spins: user.total_spins,
      used_spins: user.used_spins
    };

    // Lưu thời gian đăng nhập để quản lý session
    req.session.loginTime = Date.now();

    // Gọi API FTel để lấy thông tin đơn hàng
    try {
      const ftelResponse = await getContractByPhone(userinfo.phone_number);

      if (ftelResponse.Code === 200 && ftelResponse.Data && ftelResponse.Data.Order) {
        let totalNewSpins = 0;

        for (const order of ftelResponse.Data.Order) {
          console.log("order: ", order)
          // Kiểm tra xem order đã được xử lý chưa
          const [existingOrder] = await pool.query(
            'SELECT id FROM processed_orders WHERE order_code = ?',
            [order.OrderCode]
          );

          if (existingOrder.length === 0) {
            // Tính số camera trong đơn hàng
            let cameraCount = 0;
            let productDetails = [];

            if (order.Products) {
              for (const product of order.Products) {
                const qty = product.Qty || 0;
                cameraCount += qty;
                productDetails.push(`${product.Name} (${qty})`);

              }
            }

            if (cameraCount > 0) {
              // Lưu đơn hàng đã xử lý với fullname và service
              const serviceInfo = order.Services ? JSON.stringify(order.Services) : null;
              const [orderResult] = await pool.query(
                'INSERT INTO processed_orders (user_id, order_code, contract_code, camera_count, full_name, service) VALUES (?, ?, ?, ?, ?, ?)',
                [userId, order.OrderCode, order.Contract, cameraCount, order.FullName, serviceInfo]
              );

              // Tính lượt quay 
              const spinsToAdd = cameraCount * SPINS_PER_CAMERA_ORDER;
              totalNewSpins += spinsToAdd;

              // Ghi log cộng lượt quay với chi tiết sản phẩm
              await pool.query(
                'INSERT INTO spin_award_logs (user_id, processed_order_id, spins_awarded, reason, award_source) VALUES (?, ?, ?, ?, ?)',
                [userId, orderResult.insertId, spinsToAdd, `Mua ${cameraCount} camera (${productDetails.join(', ')}) từ đơn hàng ${order.OrderCode}`, 'PROCESSED_ORDER']
              );
            }
          }
        }
        // Cộng lượt quay đăng nhập và có mua camera
        if (totalNewSpins > 0 && !user.has_received_spin_award) {
          await pool.query(
            'UPDATE users SET total_spins = total_spins + ?, has_received_spin_award = TRUE WHERE id = ?',
            [FIRST_LOGIN_SPIN_AWARD, userId]
          );

          // Ghi log cộng lượt quay
          await pool.query(
            'INSERT INTO spin_award_logs (user_id, spins_awarded, reason, award_source) VALUES (?, ?, ?, ?)',
            [userId, 1, 'Đăng nhập lần đầu', 'FIRST_LOGIN_AWARD']
          );
        }
        console.log("totalNewSpins:", totalNewSpins)
        // Cập nhật tổng lượt quay nếu có đơn hàng mới
        if (totalNewSpins > 0) {
          await pool.query(
            'UPDATE users SET total_spins = total_spins + ?, last_call_ftel_at = NOW() WHERE id = ?',
            [totalNewSpins, userId]
          );
        } else {
          // Chỉ cập nhật thời gian gọi FTel
          await pool.query(
            'UPDATE users SET last_call_ftel_at = NOW() WHERE id = ?',
            [userId]
          );
        }
      }
    } catch (ftelError) {
      console.error('FTel API Error:', ftelError);
      // Không throw error để không ảnh hưởng đến quá trình đăng nhập
    }

    // Lấy thông tin user cuối cùng sau khi cập nhật
    [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
    user = users[0];

    // Cập nhật session với thông tin mới
    req.session.user = {
      id: user.id,
      phone_number: user.phone_number,
      full_name: user.full_name,
      total_spins: user.total_spins,
      used_spins: user.used_spins
    };

    // Cập nhật thời gian đăng nhập
    req.session.loginTime = Date.now();

    // Redirect về frontend với thông tin user
    const redirectUrl = new URL(OIDC_CONFIG.FRONTEND_REDIRECT_URL);
    redirectUrl.searchParams.set('login', 'success');
    redirectUrl.searchParams.set('phone_number', user.phone_number);
    redirectUrl.searchParams.set('full_name', user.full_name);
    redirectUrl.searchParams.set('total_spins', user.total_spins);
    redirectUrl.searchParams.set('used_spins', user.used_spins);

    res.redirect(redirectUrl.toString());

  } catch (err) {
    console.error('Callback Error:', err);

    let errorMessage = 'Xác thực thất bại';
    let errorCode = 'AUTH_ERROR';

    if (err.message.includes('Session expired')) {
      errorMessage = 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.';
      errorCode = 'SESSION_EXPIRED';
    } else if (err.message.includes('state')) {
      errorMessage = 'Xác thực state thất bại. Vui lòng đăng nhập lại.';
      errorCode = 'STATE_MISMATCH';
    } else if (err.message.includes('code')) {
      errorMessage = 'Xác thực mã ủy quyền thất bại. Vui lòng đăng nhập lại.';
      errorCode = 'CODE_INVALID';
    }

    // Redirect về frontend với thông báo lỗi
    const redirectUrl = new URL(OIDC_CONFIG.FRONTEND_REDIRECT_URL);
    redirectUrl.searchParams.set('login', 'error');
    redirectUrl.searchParams.set('error_code', errorCode);
    redirectUrl.searchParams.set('error_message', errorMessage);

    res.redirect(redirectUrl.toString());
  }
}

export const logout = async (req, res, next) => {
  try {
    res.json({
      success: true,
      message: 'Đăng xuất thành công'
    });
  } catch (err) {
    console.error('Logout Error:', err);
    next(err);
  }
}
