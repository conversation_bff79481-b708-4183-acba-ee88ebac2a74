import crypto from 'crypto';
import { BadRequestError } from '../errors/index.js';
import { redis } from '../configs/db.js';

/**
 * Middleware xác thực checksum
 * - Kiểm tra timestamp (unix timestamp milliseconds) - nếu quá 10 phút thì không xử lý
 * - Kiểm tra nonce lưu vào redis trong 10 phút để đảm bảo không bị trùng request
 * - Kiểm tra path: full path và query params
 * - Nế<PERSON> là request POST, PUT, PATCH thì thêm body nối với nhau bằng query string và sắp xếp a-z
 * - Sử dụng HMAC SHA-256 với secret key để tạo và xác thực checksum
 */
export const verifyChecksum = (secretKey) => {
  if (!secretKey) {
    throw new Error('Secret key is required for checksum verification');
  }

  return async (req, res, next) => {
    try {
      // Lấy checksum và timestamp từ header
      const checksum = req.headers['x-checksum'];
      const timestamp = req.headers['x-timestamp'];
      const nonce = req.headers['x-nonce'];

      // Kiểm tra các trường bắt buộc
      if (!checksum) {
        throw new BadRequestError('BAD_REQUEST', 'Yêu cầu không hợp lệ.');
      }

      if (!timestamp) {
        throw new BadRequestError('BAD_REQUEST', 'Yêu cầu không hợp lệ.');
      }

      if (!nonce) {
        throw new BadRequestError('BAD_REQUEST', 'Yêu cầu không hợp lệ.');
      }

      // Kiểm tra timestamp (10 phút = 600000 ms)
      const currentTime = Date.now();
      const requestTime = parseInt(timestamp, 10);

      if (isNaN(requestTime)) {
        throw new BadRequestError('BAD_REQUEST', 'Yêu cầu không hợp lệ.');
      }

      if (currentTime - requestTime > 600000) {
        throw new BadRequestError('BAD_REQUEST', 'Yêu cầu không hợp lệ.');
      }

      // Kiểm tra nonce trong Redis
      const nonceKey = `checksum:nonce:${nonce}`;
      const existingNonce = await redis.get(nonceKey);

      if (existingNonce) {
        throw new BadRequestError('BAD_REQUEST', 'Yêu cầu không hợp lệ.');
      }

      // Lưu nonce vào Redis với thời gian sống 10 phút
      await redis.set(nonceKey, timestamp, 'EX', 600);

      // Tạo chuỗi dữ liệu để tính checksum
      let data = '';

      // Thêm timestamp
      data += `timestamp=${timestamp}`;

      // Thêm nonce
      data += `&nonce=${nonce}`;

      // Thêm path và query params
      const fullPath = req.originalUrl || req.url;
      const path = fullPath.split('?')[0];

      data += `&path=${path}`;

      // Sử dụng req.query thay vì phân tích query string thủ công
      if (Object.keys(req.query).length > 0) {
        const queryParams = new URLSearchParams();
        
        // Sắp xếp keys theo thứ tự a-z để đảm bảo tính nhất quán
        const sortedKeys = Object.keys(req.query).sort();
        
        for (const key of sortedKeys) {
          if (req.query[key] !== undefined && req.query[key] !== null) {
            queryParams.append(key, req.query[key]);
          }
        }
        
        const queryString = queryParams.toString();
        if (queryString) {
          data += `&${queryString}`;
        }
      }

      // Nếu là request POST, PUT, PATCH thì thêm body
      if (['POST', 'PUT', 'PATCH'].includes(req.method) && req.body) {
        // Chuyển đổi body thành query string và sắp xếp theo key a-z
        const bodyParams = new URLSearchParams();

        // Sắp xếp keys theo thứ tự a-z
        const sortedKeys = Object.keys(req.body).sort();

        for (const key of sortedKeys) {
          if (req.body[key] !== undefined && req.body[key] !== null) {
            // Chuyển đổi giá trị thành chuỗi
            const value = typeof req.body[key] === 'object'
              ? JSON.stringify(req.body[key])
              : req.body[key].toString();

            bodyParams.append(key, value);
          }
        }

        const bodyString = bodyParams.toString();
        if (bodyString) {
          data += `&${bodyString}`;
        }
      }
      console.log('data', data);
      // Tính toán HMAC
      const calculatedChecksum = crypto
        .createHmac('sha256', secretKey)
        .update(data)
        .digest('hex');

      // So sánh checksum
      if (calculatedChecksum !== checksum) {
        throw new BadRequestError('INVALID_CHECKSUM', 'Checksum không hợp lệ');
      }

      // Checksum hợp lệ, tiếp tục xử lý
      next();
    } catch (error) {
      next(error);
    }
  };
};