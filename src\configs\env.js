/**
 * Tập trung quản lý các biến môi trường
 */

// Môi trường
export const NODE_ENV = process.env.NODE_ENV || 'development';
export const HOST = process.env.HOST || '0.0.0.0';
export const PORT = process.env.PORT || 3000;

// Database
export const DB_HOST = process.env.DB_HOST || 'localhost';
export const DB_USER = process.env.DB_USER || 'root';
export const DB_PASSWORD = process.env.DB_PASSWORD || 'Tuan123';
export const DB_NAME = process.env.DB_NAME || 'landing_fpt';
export const DB_PORT = process.env.DB_PORT || '5008';

// Redis
export const REDIS_HOST = process.env.REDIS_HOST || 'localhost';
export const REDIS_PORT = process.env.REDIS_PORT || 6379;
export const REDIS_CACHE_ENABLED = process.env.REDIS_CACHE_ENABLED === 'true' || NODE_ENV !== 'development';
export const REDIS_CACHE_TTL = parseInt(process.env.REDIS_CACHE_TTL || '60', 10); // Thời gian cache mặc định 60 giây

// Session
export const SESSION_SECRET = process.env.SESSION_SECRET || 'your-secret-key-change-in-production';

// OIDC Configuration
export const OIDC_CLIENT_ID = process.env.OIDC_CLIENT_ID || 'fli-landingpage-public-client';
export const OIDC_REDIRECT_URI = process.env.OIDC_REDIRECT_URI || 'https://dev-api-landing.nvt16.site/callback';
export const OIDC_ISSUER_URL = process.env.OIDC_ISSUER_URL || 'https://accounts-stag.fpt.vn';
export const FRONTEND_REDIRECT_URL = process.env.FRONTEND_REDIRECT_URL || 'https://api-local.nvt16.site';

// CORS
export const CORS_ORIGINS = process.env.CORS_ORIGINS ? process.env.CORS_ORIGINS.split(',').map(origin => origin.trim()) : [];

// Spin Configuration
export const SPINS_PER_CAMERA_ORDER = process.env.SPINS_PER_CAMERA_ORDER || 3;
export const FACEBOOK_SHARE_SPIN_AWARD = process.env.FACEBOOK_SHARE_SPIN_AWARD || 2;
export const FIRST_LOGIN_SPIN_AWARD = process.env.FIRST_LOGIN_SPIN_AWARD || 1;
export const CAMPAIGN_BANNER_URL = process.env.CAMPAIGN_BANNER_URL || '/images/banners/campaign-summer-2025.png';

// Captcha
export const CLOUDFLARE_TURNSTILE_SECRET_KEY = process.env.CLOUDFLARE_TURNSTILE_SECRET_KEY;

// FTEL API Configuration
export const FTEL_API_ENABLED = process.env.FTEL_API_ENABLED || 'false';
export const FTEL_API_AUTH_ENDPOINT = process.env.FTEL_API_AUTH_ENDPOINT || 'http://localhost:5080/ftel-gw/selfservice/game/api/v1/auth/token';
export const FTEL_API_CLIENT_ID = process.env.FTEL_API_CLIENT_ID || 'admin';
export const FTEL_API_CLIENT_SECRET = process.env.FTEL_API_CLIENT_SECRET || 'admin';
export const FTEL_API_ENDPOINT = process.env.FTEL_API_ENDPOINT || 'http://localhost:5080/ftel-gw/selfservice/game/api/v1/game/get-contract-by-phone';
export const FTEL_API_GAME_ID = process.env.FTEL_API_GAME_ID || '21d984a4-8a98-47a0-8bcb-dcae2ffc9820';

// API Security
export const API_SECRET_KEY = process.env.API_SECRET_KEY || 'Tuan123';

// Rate Limiting
export const RATE_LIMIT_WINDOW_MS = parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10); // 15 phút mặc định
export const RATE_LIMIT_MAX_REQUESTS = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10); // 100 request mặc định
export const LOGIN_RATE_LIMIT_WINDOW_MS = parseInt(process.env.LOGIN_RATE_LIMIT_WINDOW_MS || '3600000', 10); // 1 giờ mặc định
export const LOGIN_RATE_LIMIT_MAX_REQUESTS = parseInt(process.env.LOGIN_RATE_LIMIT_MAX_REQUESTS || '5', 10); // 5 request mặc định

// Kafka Logging
export const LOGGING_ESTOPIC = process.env.LOGGING_ESTOPIC || 'default-logs';
export const LOGGING_ESSERVER = process.env.LOGGING_ESSERVER || '';