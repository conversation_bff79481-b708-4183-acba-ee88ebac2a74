# Tài liệu API

Đây là tài liệu mô tả các API cho landing page vòng quay may mắn. Tài liệu này cung cấp thông tin về cách sử dụng các
endpoint, định dạng dữ liệu yêu cầu và phản hồi, cũng như các quy tắc xác thực.

## X<PERSON><PERSON> thực

Các endpoint yêu cầu xác thực sử dụng session-based authentication. Các session được quản lý tự động sau khi đăng nhập.

## Mã trạng thái HTTP

API sử dụng các mã trạng thái HTTP tiêu chuẩn để chỉ ra thành công hay thất bại của một yêu cầu:

| Mã  | Mô tả                     |
|-----|---------------------------|
| 200 | Thành công                |
| 201 | Tạo thành công            |
| 400 | <PERSON><PERSON><PERSON> cầu không hợp lệ      |
| 401 | Chưa xác thực             |
| 403 | Không có quyền truy cập   |
| 404 | Không tìm thấy tài nguyên |
| 500 | Lỗi máy chủ               |

## Định dạng lỗi

Khi xảy ra lỗi, API sẽ trả về một đối tượng JSON với cấu trúc sau:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Mô tả lỗi"
  }
}
```

## Phân trang

Các endpoint trả về danh sách hỗ trợ phân trang với các tham số truy vấn sau:

| Tham số | Mô tả                           | Mặc định |
|---------|---------------------------------|----------|
| page    | Số trang                        | 1        |
| limit   | Số lượng kết quả trên mỗi trang | 10       |

Kết quả phân trang sẽ có định dạng:

```json
{
  "data": [
    ...
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "total_pages": 10
  }
}
```

## Danh sách Endpoint

### Xác thực

**Endpoint:** `/auth/login`  
**Method:** GET  
**Mô tả:** Tạo URL xác thực để đăng nhập  
**Xác thực:** Không yêu cầu  
**Phản hồi:** `200 OK`

```json
{
  "success": true,
  "message": "Tạo URL xác thực thành công",
  "data": {
    "authorization_url": "https://auth.example.com/authorize?..."
  }
}
```

**Endpoint:** `/callback`  
**Method:** GET  
**Mô tả:** Callback sau khi xác thực  
**Xác thực:** Không yêu cầu  
**Query params:** code, state  
**Phản hồi:** Redirect về frontend với thông tin đăng nhập

**Endpoint:** `/auth/logout`  
**Method:** POST  
**Mô tả:** Đăng xuất  
**Xác thực:** Yêu cầu  
**Phản hồi:** `200 OK`

```json
{
  "success": true,
  "message": "Đăng xuất thành công"
}
```

### Người dùng

**Endpoint:** `/v1/users/me`  
**Method:** GET  
**Mô tả:** Lấy thông tin người dùng sau khi đăng nhập  
**Xác thực:** Yêu cầu  
**Phản hồi:** `200 OK`

```json
{
  "phone_number": "0987654321",
  "fullname": "Nguyễn Văn A",
  "total_spins": 100,
  "used_spins": 50
}
```

### Lượt quay

**Endpoint:** `/v1/spins`  
**Method:** POST  
**Mô tả:** Tạo lượt quay mới và nhận thưởng  
**Xác thực:** Yêu cầu  
**Phản hồi:** `200 OK`

```json
{
  "product_id": 1,
  "actual_product_id": 2,
  "instance_code": "ABC123",
  "product_name": "Ipad",
  "product_image": "https://example.com/image.jpg"
}
```

### Người trúng thưởng

**Endpoint:** `/v1/winners`  
**Method:** GET  
**Mô tả:** Lấy danh sách người trúng thưởng  
**Xác thực:** Không yêu cầu  
**Query params:** page, limit  
**Phản hồi:** `200 OK`

```json
{
  "data": [
    {
      "phone_number": "098xxx321",
      "full_name": "Nguyễn Văn A",
      "product": {
        "name": "Ipad",
        "image": "https://example.com/image.jpg"
      },
      "spun_at": "2023-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "total_pages": 10
  }
}
```

### Lịch sử quà

**Endpoint:** `/v1/users/me/rewards`  
**Method:** GET  
**Mô tả:** Lấy lịch sử quà của người dùng  
**Xác thực:** Yêu cầu  
**Query params:** page, limit  
**Phản hồi:** `200 OK`

```json
{
  "data": [
    {
      "spun_at": "2023-01-01T00:00:00Z",
      "product_instance": {
        "code": "GIFT123"
      },
      "product": {
        "name": "Ipad",
        "image": "https://example.com/image.jpg",
        "product_type": "PHYSICAL"
      }
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "total_pages": 10
  }
}
```

### Lượt quay bổ sung

**Endpoint:** `/v1/users/me/spins`  
**Method:** POST  
**Mô tả:** Cộng lượt quay cho người dùng  
**Xác thực:** Yêu cầu  
**Request Body:**

```json
{
  "type": "facebook"
}
```

**Phản hồi:** `201 Created`

```json
{
  "message": "Cộng lượt quay thành công",
  "total_spin": 101
}
```

### Giải thưởng

**Endpoint:** `/v1/prizes`  
**Method:** GET  
**Mô tả:** Lấy danh sách giải thưởng có sẵn  
**Xác thực:** Không yêu cầu  
**Query params:** page, limit  
**Phản hồi:** `200 OK`

```json
{
  "data": [
    {
      "id": 1,
      "name": "Ipad",
      "image": "https://example.com/image.jpg",
      "type": "PHYSICAL",
      "quantity": 10,
      "quantity_in_campaign": 5,
      "display_order": 1,
      "is_active": 1
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "total_pages": 10
  },
  "campaign_id": 1
}
```

**Endpoint:** `/v1/prizes/detailed`  
**Method:** GET  
**Mô tả:** Lấy danh sách giải thưởng chi tiết với thông tin số lượng  
**Xác thực:** Không yêu cầu  
**Phản hồi:** `200 OK`

```json
{
  "data": [
    {
      "id": 1,
      "product_name": "Ipad",
      "product_image": "https://example.com/image.jpg",
      "product_type": "PHYSICAL",
      "total_quantity": 20,
      "remaining_quantity": 15,
      "used_quantity": 5
    }
  ],
  "total": 10
}
