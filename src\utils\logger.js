import { APP_MODE } from '../configs/app.js';
import { LOGGING_ESTOPIC } from '../configs/env.js';
import { isKafkaConnected, sendKafkaMessage } from './kafkaInit.js';

// Lấy <PERSON>ka topic từ biến môi trường
const KAFKA_TOPIC = LOGGING_ESTOPIC;

/**
 * Các level log được hỗ trợ
 */
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug'
};

/**
 * Gửi log lỗi đến Kafka
 * @param {Object} error - Đ<PERSON><PERSON> tượng lỗi
 * @param {Object} request - Đ<PERSON><PERSON> tượng request
 */
export const logErrorToKafka = async (error, request) => {
    if (!isKafkaConnected()) {
        console.error('Kafka producer not connected');
        // Tiếp tục thực hiện để thử kết nối lại trong sendKafkaMessage
    }

    try {
        const logData = {
            timestamp: new Date().toISOString(),
            level: 'error',
            name: error.name || 'Error',
            message: error.message || 'Unknown error',
            stack: error.stack,
            statusCode: error.httpCode || error.statusCode || 500,
            url: request?.url,
            method: request?.method,
            ip: request?.headers?.['x-forwarded-for'] || request?.ip || '::1',
            userAgent: request?.headers?.['user-agent'] || 'Unknown',
            // Giữ lại các thông tin bổ sung cho việc debug
            code: error.code || error.errorCode,
            params: request?.params,
            query: request?.query,
            body: request?.body,
            environment: APP_MODE
        };

        await sendKafkaMessage(logData, KAFKA_TOPIC);
    } catch (kafkaError) {
        console.error('Failed to send error log to Kafka:', kafkaError);
        // Ghi log ra console nếu không thể gửi đến Kafka
        console.error('Original error:', error);
    }
};

/**
 * Ghi log lỗi (fallback ra console nếu không thể gửi đến Kafka)
 * @param {Object} error - Đối tượng lỗi
 * @param {Object} request - Đối tượng request
 */
/**
 * Ghi log với level tùy chọn
 * @param {String} level - Level của log (error, warn, info, debug)
 * @param {Object} data - Dữ liệu cần ghi log
 * @param {Object} request - Đối tượng request (nếu có)
 */
export const log = async (level, data, request = null) => {
    // Chuẩn bị dữ liệu log
    const logData = {
        timestamp: new Date().toISOString(),
        level: level || LOG_LEVELS.INFO,
        name: data.name || 'ApplicationLog',
        message: data.message || '',
        ...data
    };
    
    // Thêm thông tin từ request nếu có
    if (request) {
        logData.url = request.url;
        logData.method = request.method;
        logData.ip = request.headers?.['x-forwarded-for'] || request.ip || '::1';
        logData.userAgent = request.headers?.['user-agent'] || 'Unknown';
    }
    
    // Gửi log đến Kafka
    try {
        await sendKafkaMessage(logData, KAFKA_TOPIC);
    } catch (error) {
        console.error('Failed to send log to Kafka:', error);
    }
    
    // Ghi log ra console
    const consoleMethod = level === LOG_LEVELS.ERROR ? 'error' : 
                         level === LOG_LEVELS.WARN ? 'warn' : 
                         level === LOG_LEVELS.DEBUG ? 'debug' : 'log';
    
    console[consoleMethod](`[${level.toUpperCase()}]`, logData);
};

/**
 * Ghi log lỗi (sử dụng mẫu log mới)
 * @param {Object} error - Đối tượng lỗi
 * @param {Object} request - Đối tượng request
 */
export const logError = async (error, request) => {
    // Gửi log đến Kafka - sendKafkaMessage sẽ tự động thử kết nối lại nếu cần
    await logErrorToKafka(error, request);
    
    // Ghi log ra console trong mọi trường hợp để đảm bảo không mất thông tin
    console.error('Error occurred:', {
        level: LOG_LEVELS.ERROR,
        name: error.name || 'Error',
        message: error.message,
        statusCode: error.httpCode || error.statusCode || 500,
        url: request?.url,
        method: request?.method
    });
};

/**
 * Ghi log cảnh báo
 * @param {String} message - Thông báo cảnh báo
 * @param {Object} data - Dữ liệu bổ sung
 * @param {Object} request - Đối tượng request (nếu có)
 */
export const logWarn = (message, data = {}, request = null) => {
    return log(LOG_LEVELS.WARN, { message, ...data }, request);
};

/**
 * Ghi log thông tin
 * @param {String} message - Thông báo thông tin
 * @param {Object} data - Dữ liệu bổ sung
 * @param {Object} request - Đối tượng request (nếu có)
 */
export const logInfo = (message, data = {}, request = null) => {
    return log(LOG_LEVELS.INFO, { message, ...data }, request);
};

/**
 * Ghi log debug
 * @param {String} message - Thông báo debug
 * @param {Object} data - Dữ liệu bổ sung
 * @param {Object} request - Đối tượng request (nếu có)
 */
export const logDebug = (message, data = {}, request = null) => {
    return log(LOG_LEVELS.DEBUG, { message, ...data }, request);
};