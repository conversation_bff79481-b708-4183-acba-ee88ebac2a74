import mysql from 'mysql2/promise';
import Redis from 'ioredis';
import { REDIS_HOST, REDIS_PORT, DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME } from './env.js';

const redis = new Redis({
    host: REDIS_HOST,
    port: REDIS_PORT,
    db: 0,
    maxRetriesPerRequest: null
});

// Cấu hình kết nối MySQL
const dbConfig = {
    host: DB_HOST,
    port: DB_PORT,
    user: DB_USER,
    password: DB_PASSWORD,
    database: DB_NAME,
    waitForConnections: true,
    connectionLimit: 20,
    queueLimit: 1,
    timezone: '+07:00',
    dateStrings: true,
};

// Tạo pool connection
const pool = mysql.createPool(dbConfig);

// Ki<PERSON>m tra kết nối
const testConnection = async () => {
    try {
        const connection = await pool.getConnection();
        console.log('Database connection established successfully');
        connection.release();
        return true;
    } catch (error) {
        console.error('Database connection failed:', error.message);
        return false;
    }
};

export { pool, testConnection, redis };