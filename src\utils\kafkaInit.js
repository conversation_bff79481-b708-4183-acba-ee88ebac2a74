import { Kafka } from 'kafkajs';
import { LOGGING_ESSERVER, LOGGING_ESTOPIC } from '../configs/env.js';

// Khởi tạo Kafka client và producer
let kafka = null;
let producer = null;
let isConnected = false;

/**
 * Khởi tạo kết nối <PERSON>
 */
export const initKafka = async () => {
  // Lấy cấu hình Kafka từ biến môi trường
  const KAFKA_BROKERS = LOGGING_ESSERVER ? LOGGING_ESSERVER.split(',') : [];
  
  // Chỉ khởi tạo Kafka nếu có cấu hình brokers
  if (KAFKA_BROKERS.length > 0) {
    try {
      kafka = new Kafka({
        clientId: 'api-landing',
        brokers: KAFKA_BROKERS,
        retry: {
          initialRetryTime: 100,
          retries: 3
        }
      });
      
      producer = kafka.producer();
      
      // Kết nối đến <PERSON> broker
      await producer.connect();
      isConnected = true;
      console.log('Kafka producer connected successfully');
      
      // Xử lý khi ứng dụng đóng
      process.on('exit', async () => {
        if (producer && isConnected) {
          await producer.disconnect();
          isConnected = false;
        }
      });
      
      return producer;
    } catch (error) {
      console.error('Error initializing Kafka:', error);
      isConnected = false;
      producer = null;
      return null;
    }
  }
  
  return null;
};

/**
 * Lấy Kafka producer đã được khởi tạo
 */
export const getKafkaProducer = () => producer;

/**
 * Kiểm tra xem Kafka producer có được kết nối không
 */
export const isKafkaConnected = () => isConnected && producer !== null;

/**
 * Lấy thông tin trạng thái kết nối Kafka
 * @returns {Object} Thông tin trạng thái
 */
export const getKafkaStatus = () => {
  return {
    isConnected,
    hasProducer: producer !== null,
    brokers: LOGGING_ESSERVER ? LOGGING_ESSERVER.split(',') : [],
    topic: LOGGING_ESTOPIC
  };
};

/**
 * Thử kết nối lại với Kafka nếu mất kết nối
 */
export const reconnectKafka = async () => {
  if (!producer || !isConnected) {
    return await initKafka();
  }
  return producer;
};

/**
 * Gửi message đến Kafka
 * @param {Object} message - Message cần gửi
 * @param {String} topic - Topic để gửi message (mặc định là LOGGING_ESTOPIC)
 */
export const sendKafkaMessage = async (message, topic = LOGGING_ESTOPIC) => {
  if (!isConnected || !producer) {
    console.error('Kafka producer not connected');
    // Thử kết nối lại trước khi từ bỏ
    const reconnected = await reconnectKafka();
    if (!reconnected) {
      return false;
    }
  }
  
  try {
    await producer.send({
      topic,
      messages: [
        { value: JSON.stringify(message) }
      ]
    });
    return true;
  } catch (error) {
    console.error('Failed to send message to Kafka:', error);
    isConnected = false; // Đánh dấu là mất kết nối để thử lại sau
    return false;
  }
};