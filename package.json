{"name": "api-landing-v2", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"dev": "node --watch --env-file=.env server.js", "start": "node --env-file=.env.staging server.js", "start:dev": "node --env-file=.env.development server.js", "start:staging": "node --env-file=.env.staging server.js", "start:production": "node --env-file=.env.production server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "compression": "^1.8.1", "connect-redis": "^7.1.1", "cors": "^2.8.5", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-session": "^1.18.1", "express-validator": "^7.2.1", "global-agent": "^3.0.0", "helmet": "^8.1.0", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "ioredis": "^5.6.1", "kafkajs": "^2.2.4", "mysql2": "^3.14.2", "openid-client": "^6.6.2", "rate-limit-redis": "^4.2.1", "undici": "^7.12.0", "winston": "^3.17.0"}}