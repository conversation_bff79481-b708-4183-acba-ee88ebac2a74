# Database Configuration - <PERSON><PERSON><PERSON> hình cơ sở dữ liệu
DB_HOST=localhost                # Địa chỉ máy chủ cơ sở dữ liệu
DB_PORT=3306                    # Cổng kết nối cơ sở dữ liệu
DB_USER=root                    # Tên người dùng cơ sở dữ liệu
DB_PASSWORD=Tuan123             # Mật khẩu cơ sở dữ liệu
DB_NAME=landing_fpt             # Tên cơ sở dữ liệu

# Redis Configuration - Cấu hình Redis
REDIS_HOST=localhost            # Địa chỉ máy chủ Redis
REDIS_PORT=6379                 # Cổng kết nối Redis

# Session Configuration - Cấu hình phiên làm việc
SESSION_SECRET=Tuan123          # Khóa bí mật cho phiên làm việc

# OIDC Configuration for FPT ID - Cấu hình OIDC cho FPT ID
OIDC_CLIENT_ID=fli-landingpage-public-client    # ID khách hàng OIDC
# http://api-landing với domain public
OIDC_REDIRECT_URI=https://api-landing/callback  # URI chuyển hướng sau khi xác thực
# http://account-fpt
OIDC_ISSUER_URL=https://accounts-stag.fpt.vn    # URL nhà cung cấp dịch vụ OIDC
# http://landing
FRONTEND_REDIRECT_URL=https://landing/su-kien/combo-1-nha/  # URL chuyển hướng đến trang landing frontend khi xác thực thành công

# Spin Configuration - Cấu hình vòng quay
SPINS_PER_CAMERA_ORDER=3        # Số lượt quay thưởng cho mỗi camera
FACEBOOK_SHARE_SPIN_AWARD=2     # Số lượt quay thưởng khi chia sẻ Facebook
FIRST_LOGIN_SPIN_AWARD=1        # Số lượt quay thưởng khi đăng nhập lần đầu và có đơn hàng
CAMPAIGN_BANNER_URL=/images/banners/campaign-summer-2025.png  # URL banner chiến dịch

# Environment - Cấu hình môi trường
NODE_ENV=staging                # Môi trường chạy ứng dụng (development, staging, production)
HOST=0.0.0.0                    # Địa chỉ IP máy chủ
PORT=3005                       # Cổng máy chủ

CORS_ORIGINS=http://localhost:3000,https://dev-api-landing.nvt16.site  # Danh sách nguồn được phép truy cập CORS

FTEL_API_ENABLED=false          # Bật/tắt tích hợp API FTEL - không sử dụng nữa
# service api ftel - Cấu hình API FTEL
FTEL_API_ENDPOINT=http://ftel/ftel-gw/selfservice/game/api/v1/game/get-contract-by-phone  # Endpoint API FTEL
FTEL_API_AUTH_ENDPOINT=http://ftel/ftel-gw/selfservice/game/api/v1/auth/token  # Endpoint xác thực API FTEL
FTEL_API_CLIENT_ID=admin        # ID khách hàng API FTEL
FTEL_API_CLIENT_SECRET=admin    # Khóa bí mật API FTEL
FTEL_API_GAME_ID=21d984a4-8a98-47a0-8bcb-dcae2ffc9820  # ID trò chơi API FTEL

#LOGGING - Cấu hình ghi log
LOGGING_ESTOPIC=stag-spin-wheel-game-logs  # Tên topic Kafka cho log
LOGGING_ESSERVER=localhost:9092  # Địa chỉ máy chủ Kafka

#Proxy - Cấu hình proxy
HTTP_PROXY=http://isc-proxy.hcm.fpt.vn:80  # Địa chỉ proxy HTTP
NO_PROXY=saleplatform-api-stag.fpt.net
# Rate Limiting - Giới hạn tần suất truy cập
RATE_LIMIT_WINDOW_MS=60000      # Khoảng thời gian giới hạn tần suất (mili giây)
RATE_LIMIT_MAX_REQUESTS=1000    # Số lượng yêu cầu tối đa trong khoảng thời gian
LOGIN_RATE_LIMIT_WINDOW_MS=60000  # Khoảng thời gian giới hạn đăng nhập (mili giây)
LOGIN_RATE_LIMIT_MAX_REQUESTS=50  # Số lượng yêu cầu đăng nhập tối đa trong khoảng thời gian