FROM registry-isc.fpt.net/nodejs/node21.5.0-bullseye-sonar-scanner

ENV http_proxy http://proxy.hcm.fpt.vn:80
ENV https_proxy http://proxy.hcm.fpt.vn:80

COPY package.json /app/
COPY package-lock.json /app/

WORKDIR /app

#COPY ["${SERVICE_NAME}/package.json", "${SERVICE_NAME}/package-lock.json*", "./"]
## Nếu dự án có sử dụng Package Registry của gitlab được lưu ở repository bên dev
##      - Thực hiện copy thêm file .npmrc
##      - <PERSON><PERSON><PERSON> thời yêu câu dev define scripts trong file package.json
##          "preinstall": "npm config set https-proxy http://proxy.hcm.fpt.vn:80 && npm config set proxy http://proxy.hcm.fpt.vn:80 && npm config set strict-ssl false && npm config list && npm config set noproxy gitlab.fpt.net",
##          "postinstall": "npm config list"
## Tham khảo source: http://gitlab.fpt.net/customer-services-system/aimoment-webapp/-/blob/main/package.json
#COPY ["${SERVICE_NAME}/package.json", "${SERVICE_NAME}/package-lock.json*", "${SERVICE_NAME}/.npmrc", "./"]
# RUN npm run preinstall
RUN npm install --legacy-peer-deps
ENV http_proxy ''
ENV https_proxy ''