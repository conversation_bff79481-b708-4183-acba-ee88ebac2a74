#!/bin/bash

## CI/CD variables Configuration
##! - adding only non-sensitive variables, like a BUILD_ENV or REPO_URL variable
##! - adding sensitive variables containing secrets, keys, and so on in repository settings, NOT IN the env.sh file
## Project Settings
##! PROJECT_FULLNAME: This is an Identifier of System Configuration.
##!     A part of naming: sonaqube, harbor, k8s_namespace,  get-kube-config.sh...
##! 
##! Inherited Variables:
##!     IDENTIFIER_FPROJECT: tương ứng field Identifier trên FPT Service Management
##!     Manual add to Group Settings - level: Project
##!         - IDENTIFIER_FPROJECT | <text> | unmarked | All (default)
##! Variables:
export PARENT_PROJECT=ftel-game
export PROJECT_NAME=spin-wheel-game
export PROJECT_FULLNAME=spin-wheel-game
## Service Setiings
##! Variables:
export SERVICE_NAME=spin-wheel-game-api
export REPO_URL=$(echo "${GIT_HOST_URL}/${GIT_PREFIX}/${PROJECT_FULLNAME}/${SERVICE_NAME}" | tr -s "/")
#export SYS_CONFIG_REPO_URL=$(echo "${GIT_HOST_URL}/${GIT_PREFIX}/SYS/${PROJECT_FULLNAME}/cmt-helm-config" | tr -s "/")
## Gitlab Authentication 
##! Inherited Variables:
##!     Manual add to Group Settings - level: Project
##!     Go to Group Settings: Settings > CI/CD and expand the Variables section.
##!         - CI_GIT_CLONE_TOKEN | <Base64 alphabet> | marked | All (default)
##!         - CI_SYS_GROUP_ACCESS_TOKEN | <Base64 alphabet> | marked | All (default)
## Gong Settings
##! Variables:
##!     Default: using IDENTIFIER_FPROJECT
##!     If null, using variable PROJECT_FULLNAME. This is a temporary solution
if [[ -z $IDENTIFIER_FPROJECT ]] || [ "$IDENTIFIER_FPROJECT" != " " ]; then
    export GONG_UPCODE_PROJECT_NAME="$PROJECT_FULLNAME"
else 
    export GONG_UPCODE_PROJECT_NAME="$IDENTIFIER_FPROJECT"
fi;
## Registry Settings
##! Inherited Variables:
##!     - REGISTRY_URL: Available in Group Setting - level: SYS
##!
##! Variables:

export REGISTRY_URI=$(echo "/${PROJECT_FULLNAME}/${SERVICE_NAME}" | tr -s "/")
## Vault Settings
export VAULT_PROJECT_PATH=$(echo "isc-project/${PARENT_PROJECT}/${PROJECT_FULLNAME}" | tr -s "/")
export VAULT_PROJECT_ROLE=$(echo "isc-project-${PARENT_PROJECT}-${PROJECT_FULLNAME}" | tr -s "-")
##!
## Registry Authentication
##! Inherited Variables:
##!     Manual add to Group Settings - level: Project
##!     Go to group settings: Settings > CI/CD and expand the Variables section.
##!         - REGISTRY_USER | <PROJECT_FULLNAME> | unmarked | All (default)
##!         - REGISTRY_PASS | <Base64 alphabet> | marked | All (default)
## Sonarqube Settings
##! Inherited Variables:
##!     Manual add to Group Settings - level: Project
##!     Go to Group Settings: Settings > CI/CD and expand the Variables section.
##!         - SONAR_HOST_URL: Available in Group Setting - level: SYS 
##! Variables:
export SONAR_PROJECT_KEY="$SERVICE_NAME"
export SONAR_USERNAME="${PROJECT_NAME}"
## Sonarqube Authentication
##! Inherited Variables:
##!     Manual add to Group Settings - level: Project
##!     Go to group settings: Settings > CI/CD and expand the Variables section.
##!         - SONAR_TOKEN | <Base64 alphabet> | marked | All (default)
##!         - SONAR_VIEW_PASS | <text> | unmarked | All (default)
## Pipeline Settings
export BUILD_PATH="build/image"
export DEPLOY_PATH="deploy/$SERVICE_NAME"
export CLONE_PATH="config"
###! CI Settings

export DOCKER_FILE="$BUILD_PATH/Dockerfile"
export DOCKER_FILE_BUILD_TEST="$BUILD_PATH/Dockerfile.test"
export DOCKER_FILE_W_SONAR="$BUILD_PATH/Dockerfile.sonar"
### BASE IMAGE SETTINGS
export BASE_IMAGE_BUILD_PATH=build/base-image
## BUILD IMAGE
export BUILD_IMAGE_TAG="latest"
export BUILD_IMAGE_REGISTRY_URI=$(echo "/${PROJECT_FULLNAME}/build" | tr -s "/")
export BUILD_IMAGE=${REGISTRY_URL}${BUILD_IMAGE_REGISTRY_URI}:${BUILD_IMAGE_TAG}
export BUILD_IMAGE_DOCKER_FILE=${BASE_IMAGE_BUILD_PATH}/Dockerfile.build.base-image
## RUNTIME IMAGE
export RUNTIME_IMAGE_TAG="latest"
export RUNTIME_IMAGE_REGISTRY_URI=$(echo "/$PROJECT_FULLNAME/runtime" | tr -s "/")
export RUNTIME_IMAGE=${REGISTRY_URL}${RUNTIME_IMAGE_REGISTRY_URI}:${RUNTIME_IMAGE_TAG}

export RUNTIME_IMAGE_DOCKER_FILE=${BASE_IMAGE_BUILD_PATH}/Dockerfile.runtime.base-image
