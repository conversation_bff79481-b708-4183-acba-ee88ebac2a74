# Hướng dẫn

Hướng dẫn setup dự án trên 3 môi trường development, staging, production với dockerfile

## Huơngs dẫn setup Dockerfile

### Development

yêu cầu có file .env.development

```Dockerfile
FROM node:22.17.1-alpine

WORKDIR /app

COPY package.json package-lock.json ./

RUN npm install

COPY . .

ENV HOST=0.0.0.0
ENV PORT=3000
EXPOSE 3000

CMD ["npm", "run", "start:dev"]
```

### Staging

yêu cầu có file .env.staging

```Dockerfile
FROM node:22.17.1-alpine

WORKDIR /app

COPY package.json package-lock.json ./

RUN npm install

COPY . .

ENV HOST=0.0.0.0
ENV PORT=3000
EXPOSE 3000

CMD ["npm", "run", "start:staging"]
```

### Production

yêu cầu có file .env.production

```Dockerfile
FROM node:22.17.1-alpine

WORKDIR /app

COPY package.json package-lock.json ./

RUN npm install

COPY . .

ENV HOST=0.0.0.0
ENV PORT=3000
EXPOSE 3000

CMD ["npm", "run", "start:production"]
```

## Hướng dẫn setup env

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=Tuan123
DB_NAME=landing_fpt

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Session Configuration
SESSION_SECRET=Tuan123

# OIDC Configuration for FPT ID
OIDC_CLIENT_ID=fli-landingpage-public-client
OIDC_REDIRECT_URI=https://dev-api-landing.nvt16.site/callback
OIDC_ISSUER_URL=https://accounts-stag.fpt.vn
FRONTEND_REDIRECT_URL=https://dev-api-landing.nvt16.site/landing/

# Spin Configuration
SPINS_PER_CAMERA_ORDER=3
FACEBOOK_SHARE_SPIN_AWARD=2
FIRST_LOGIN_SPIN_AWARD=1
CAMPAIGN_BANNER_URL=/images/banners/campaign-summer-2025.png

# Environment
NODE_ENV=staging
HOST=0.0.0.0
PORT=3005

CORS_ORIGINS=http://localhost:3000,https://dev-api-landing.nvt16.site

FTEL_API_ENABLED=false
FTEL_API_ENDPOINT=http://localhost:5080/ftel-gw/selfservice/game/api/v1/game/get-contract-by-phone
FTEL_API_AUTH_ENDPOINT=http://localhost:5080/ftel-gw/selfservice/game/api/v1/auth/token
FTEL_API_CLIENT_ID=admin
FTEL_API_CLIENT_SECRET=admin
FTEL_API_GAME_ID=21d984a4-8a98-47a0-8bcb-dcae2ffc9820
#LOGGING
LOGGING_ESTOPIC=stag-spin-wheel-game-logs
LOGGING_ESSERVER=localhost:9092

#Proxy
HTTP_PROXY=http://isc-proxy.hcm.fpt.vn:80

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000
LOGIN_RATE_LIMIT_WINDOW_MS=60000
LOGIN_RATE_LIMIT_MAX_REQUESTS=50
```
