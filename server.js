import express from "express"
import cors from "cors"
import session from "express-session"
import RedisStore from "connect-redis"
import helmet from "helmet"
import compression from "compression"
import router from "./src/routes/index.js"
import { notFoundHandler, errorHandler } from "./src/middlewares/errorHandler.js"
import { testConnection, redis } from "./src/configs/db.js"
import { initializeOIDC } from "./src/configs/oidc.js"
import { initKafka } from "./src/utils/kafkaInit.js"
import { CORS_ORIGINS, SESSION_SECRET, NODE_ENV, HOST, PORT } from "./src/configs/env.js"

// Kiểm tra kết nối database
await testConnection()

// Khởi tạo OIDC client
try {
    await initializeOIDC();
    console.log('OIDC client initialized successfully');
} catch (error) {
    console.error('Failed to initialize OIDC client:', error);
    process.exit(1);
}

// Khởi tạo Kafka producer
try {
    const producer = await initK<PERSON>ka();
    if (producer) {
        console.log('Kafka producer initialized successfully');
    } else {
        console.log('Kafka producer not initialized - logging to console only');
        console.log('Check your LOGGING_ESSERVER environment variable: ' + process.env.LOGGING_ESSERVER);
    }
} catch (error) {
    console.error('Failed to initialize Kafka producer:', error);
    console.log('Continuing without Kafka logging...');
}

// Thêm xử lý khi ứng dụng nhận tín hiệu SIGINT hoặc SIGTERM
process.on('SIGINT', async () => {
    console.log('Received SIGINT signal. Shutting down gracefully...');
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('Received SIGTERM signal. Shutting down gracefully...');
    process.exit(0);
});

const app = express()
app.set('trust proxy', 1);
// Thêm helmet middleware để tăng cường bảo mật
app.use(helmet());

// Thêm compression middleware để nén response
app.use(compression());

// Cấu hình CORS từ biến môi trường
app.use(cors({
    origin: CORS_ORIGINS,
    credentials: true
}))

// Cấu hình session middleware với Redis store
app.use(session({
    store: new RedisStore({ client: redis }),
    secret: SESSION_SECRET,
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // true nếu sử dụng HTTPS trong production
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
    },
    name: 'sessionId' // Đổi tên session cookie
}));

app.use(express.json())
app.use(express.urlencoded({ extended: true }))

app.use(router)

app.use(notFoundHandler);

app.use(errorHandler);

app.listen(PORT, HOST, () => {
    console.log(`Server is running on ${HOST}:${PORT}`)
    console.log("Spin worker is running and ready to process jobs")
})

