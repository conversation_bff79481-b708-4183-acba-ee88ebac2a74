import { pool } from "../configs/db.js";

// GET /v1/prizes/detailed - L<PERSON>y danh sách gi<PERSON>i thưởng chi tiết với số lượng
export const getDetailedPrizes = async (req, res, next) => {
  try {
    const connection = await pool.getConnection();
    try {
      // Lấy danh sách sản phẩm với thông tin số lượng từ product_instances
      const [rows] = await connection.query(
        `SELECT 
           p.id,
           p.product_name,
           p.product_image,
           p.product_type,
           COUNT(pi.id) as total_quantity,
           SUM(CASE WHEN pi.status = 'AVAILABLE' THEN 1 ELSE 0 END) as remaining_quantity,
           SUM(CASE WHEN pi.status = 'USED' THEN 1 ELSE 0 END) as used_quantity
         FROM products p
         LEFT JOIN product_instances pi ON p.id = pi.product_id
         GROUP BY p.id, p.product_name, p.product_image, p.product_type
         HAVING COUNT(pi.id) > 0
         ORDER BY p.id`
      );
      // Trả về kết quả
      res.json({
        data: rows,
        total: rows.length
      });
    } finally {
      connection.release();
    }
  } catch (err) {
    next(err);
  }
};

export const getPrizes = async (req, res, next) => {
  try {
    console.log("call: ", 1)
    // Lấy tham số phân trang từ query params
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const offset = (page - 1) * limit;

    const connection = await pool.getConnection();

    try {
      // Tìm chiến dịch hiện tại (đang active và trong khoảng thời gian hiệu lực)
      const [campaignResult] = await connection.query(
        "SELECT id FROM campaigns WHERE is_active = 1 AND NOW() BETWEEN start_date AND end_date ORDER BY id DESC LIMIT 1"
      );

      if (campaignResult.length === 0) {
        return res.json({
          data: [],
          pagination: {
            total: 0,
            page: page,
            limit: limit,
            total_pages: 0
          },
          message: "Không có chiến dịch nào đang hoạt động"
        });
      }

      const campaignId = campaignResult[0].id;

      // Đếm tổng số phần thưởng trong chiến dịch hiện tại
      // Loại bỏ các prize con nếu chúng nằm trong group
      const [countResult] = await connection.query(
        `SELECT COUNT(*) as total 
         FROM campaign_prizes cp 
         INNER JOIN products p ON cp.product_id = p.id 
         WHERE cp.campaign_id = ? AND cp.is_active = 1
           AND cp.id NOT IN (
             SELECT DISTINCT cpg.child_campaign_prize_id 
             FROM campaign_prize_groups cpg 
             WHERE cpg.is_active = 1
           )`,
        [campaignId]
      );
      const total = countResult[0].total;
      const totalPages = Math.ceil(total / limit);

      // Lấy danh sách phần thưởng của chiến dịch hiện tại với phân trang
      // Loại bỏ các prize con nếu chúng nằm trong group
      const [rows] = await connection.query(
        `SELECT 
           p.id, 
           p.product_name as name, 
           p.product_image as image, 
           cp.prize_type as type, 
           p.remaining_quantity as quantity,
           cp.quantity_in_campaign,
           cp.display_order,
           cp.is_active
         FROM campaign_prizes cp 
         INNER JOIN products p ON cp.product_id = p.id 
         WHERE cp.campaign_id = ? AND cp.is_active = 1
           AND cp.id NOT IN (
             SELECT DISTINCT cpg.child_campaign_prize_id 
             FROM campaign_prize_groups cpg 
             WHERE cpg.is_active = 1
           )
         ORDER BY cp.display_order, cp.id 
         LIMIT ? OFFSET ?`,
        [campaignId, limit, offset]
      );

      // Trả về kết quả với định dạng theo API spec
      res.json({
        data: rows,
        pagination: {
          total: total,
          page: page,
          limit: limit,
          total_pages: totalPages
        },
        campaign_id: campaignId
      });
    } finally {
      connection.release();
    }
  } catch (err) {
    next(err);
  }
}