import { ApiError, NotFoundError } from '../errors/index.js';
import { APP_MODE } from '../configs/app.js';
import { logError } from '../utils/logger.js';

/**
 * Middleware xử lý lỗi chung cho toàn bộ ứng dụng
 */
export const errorHandler = (err, req, res, next) => {
  // Ghi log lỗi sử dụng logger mới (ghi vào Kafka hoặc fallback ra console)
  // Đảm bảo lỗi có đầy đủ thông tin cần thiết cho log model
  if (!err.statusCode && err.httpCode) {
    err.statusCode = err.httpCode;
  }


  // Nếu là ApiError custom
  if (err instanceof ApiError) {
    logError(err, req);
    return res.status(err.httpCode).json(err.toJSON());
  }

  // Xử lý lỗi validation từ express-validator
  if (err.type === 'validation') {
    return res.status(400).json({
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Dữ liệu đầu vào không hợp lệ',
        details: err.errors
      }
    });
  }

  // Xử lý lỗi database
  if (err.code === 'ER_DUP_ENTRY') {
    return res.status(400).json({
      error: {
        code: 'DUPLICATE_ENTRY',
        message: 'Dữ liệu đã tồn tại'
      }
    });
  }

  if (err.code === 'ER_NO_REFERENCED_ROW_2') {
    return res.status(400).json({
      error: {
        code: 'FOREIGN_KEY_CONSTRAINT',
        message: 'Dữ liệu tham chiếu không tồn tại'
      }
    });
  }

  // Lỗi không xác định - Internal Server Error
  res.status(500).json({
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Đã xảy ra lỗi máy chủ'
    }
  });
};

/**
 * Middleware xử lý 404 - Not Found
 */
export const notFoundHandler = (req, res, next) => {
  const error = new NotFoundError('ENDPOINT_NOT_FOUND', `Endpoint ${req.method} ${req.path} không tồn tại`);
  next(error);
};