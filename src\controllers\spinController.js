import { pool } from '../configs/db.js';
import { FACEBOOK_SHARE_SPIN_AWARD } from '../configs/settings.js';

// POST /v1/spins - Tạo lượt quay mới và nhận thưởng
export const spin = async (req, res, next) => {
  let connection;

  try {
    const userId = req.user.id; // Từ middleware auth

    // Khởi tạo transaction MySQL
    connection = await pool.getConnection();
    await connection.beginTransaction();

    // Lock row user để đảm bảo tính nhất quán
    const [userRows] = await connection.execute(
      'SELECT id, total_spins, used_spins FROM users WHERE id = ? AND deleted_at IS NULL FOR UPDATE',
      [userId]
    );

    if (userRows.length === 0) {
      await connection.rollback();
      return res.status(404).json({
        error: {
          code: 'USER_NOT_FOUND',
          message: 'Không tìm thấy người dùng'
        }
      });
    }

    const user = userRows[0];
    const remainingSpins = user.total_spins - user.used_spins;

    // Kiểm tra số lượt quay còn lại
    if (remainingSpins <= 0) {
      await connection.rollback();
      return res.status(400).json({
        error: {
          code: 'NO_SPINS_REMAINING',
          message: 'Không còn lượt quay'
        }
      });
    }

    // Tăng used_spins lên 1 và đảm bảo không bao giờ có lượt quay âm
    const [updateSpinResult] = await connection.execute(
      'UPDATE users SET used_spins = used_spins + 1 WHERE id = ? AND (total_spins - used_spins) > 0',
      [userId]
    );
    
    // Kiểm tra xem việc cập nhật có thành công không
    if (updateSpinResult.affectedRows === 0) {
      await connection.rollback();
      return res.status(400).json({
        error: {
          code: 'NO_SPINS_REMAINING',
          message: 'Không còn lượt quay'
        }
      });
    }

    // Lấy campaign hiện tại theo start_date và end_date
    const [campaignRows] = await connection.execute(
      'SELECT id FROM campaigns WHERE is_active = 1 AND start_date <= NOW() AND end_date >= NOW() LIMIT 1'
    );

    if (campaignRows.length === 0) {
      await connection.rollback();
      return res.status(400).json({
        error: {
          code: 'NO_ACTIVE_CAMPAIGN',
          message: 'Không có chiến dịch nào đang hoạt động'
        }
      });
    }

    const campaignId = campaignRows[0].id;

    // Lấy danh sách phần thưởng và số lượng từ campaign_prizes với khóa FOR UPDATE
    const [prizeRows] = await connection.execute(`
        SELECT cp.id, cp.product_id, cp.quantity_in_campaign, cp.prize_type, p.product_name
        FROM campaign_prizes cp
        INNER JOIN products p ON cp.product_id = p.id
        WHERE cp.campaign_id = ? AND cp.quantity_in_campaign > 0 AND cp.is_active = 1 AND cp.prize_type <> 'GROUP_GIFT'
        ORDER BY cp.display_order, cp.id
        FOR UPDATE
    `, [campaignId]);

    if (prizeRows.length === 0) {
      await connection.rollback();
      return res.status(400).json({
        error: {
          code: 'NO_PRIZE',
          message: 'Không có giải thưởng nào trong chiến dịch'
        }
      });
    }

    // Tính tổng số lượng giải thưởng và xác định phần thưởng
    const totalQuantity = prizeRows.reduce((sum, prize) => sum + prize.quantity_in_campaign, 0);

    if (totalQuantity === 0) {
      await connection.rollback();
      return res.status(400).json({
        error: {
          code: 'NO_PRIZE',
          message: 'Không có giải thưởng nào'
        }
      });
    }

    // Chọn số ngẫu nhiên từ 1 đến tổng số lượng
    const randomNumber = Math.floor(Math.random() * totalQuantity) + 1;

    let currentQuantity = 0;
    let selectedPrize = null;

    // Duyệt mảng để tìm prize trúng dựa trên khoảng quantity_in_campaign
    for (const prize of prizeRows) {
      currentQuantity += prize.quantity_in_campaign;
      if (randomNumber <= currentQuantity) {
        selectedPrize = prize;
        break;
      }
    }

    // Nếu không chọn được giải nào (trường hợp edge case), chọn giải đầu tiên
    if (!selectedPrize) {
      selectedPrize = prizeRows[0];
    }

    console.log(selectedPrize);

    // Kiểm tra và trừ số lượng giải thưởng
    const [updateResult] = await connection.execute(
      'UPDATE campaign_prizes SET quantity_in_campaign = quantity_in_campaign - 1 WHERE id = ? AND quantity_in_campaign > 0',
      [selectedPrize.id]
    );

    console.log("updateResult", updateResult);

    if (updateResult.affectedRows === 0) {
      console.log("PRIZE_OUT_OF_STOCK");
      await connection.rollback();
      return res.status(400).json({
        error: {
          code: 'PRIZE_OUT_OF_STOCK',
          message: 'Giải thưởng đã hết'
        }
      });
    }

    // Xử lý logic GROUP_GIFT
    let actualProductId = selectedPrize.product_id;
    let campaignPrizeGroupId = null;
    let finalProductName = selectedPrize.product_name;
    let returnProductId = selectedPrize.product_id; // ID để trả về cho client

    // Kiểm tra xem actualProductId có nằm trong campaign_prize_groups không
    // Nếu có thì trả về product_id của group
    const [groupCheck] = await connection.execute(`
      SELECT cpg.campaign_prize_id, cp_parent.product_id as group_product_id
      FROM campaign_prize_groups cpg
      INNER JOIN campaign_prizes cp_child ON cpg.child_campaign_prize_id = cp_child.id
      INNER JOIN campaign_prizes cp_parent ON cpg.campaign_prize_id = cp_parent.id
      WHERE cp_child.product_id = ? AND cpg.is_active = 1
      LIMIT 1
    `, [actualProductId]);

    if (groupCheck.length > 0) {
      returnProductId = groupCheck[0].group_product_id;
    }

    // Tìm product_instance có sẵn với status = AVAILABLE (sử dụng actualProductId) với khóa FOR UPDATE
    const [instanceRows] = await connection.execute(
      'SELECT id, instance_code FROM product_instances WHERE product_id = ? AND status = "AVAILABLE" LIMIT 1 FOR UPDATE',
      [actualProductId]
    );

    console.log("instanceRows", instanceRows);

    if (instanceRows.length === 0) {
      console.log("NO_AVAILABLE_INSTANCE");
      await connection.rollback();
      return res.status(400).json({
        error: {
          code: 'NO_AVAILABLE_INSTANCE',
          message: 'Không có sản phẩm nào khả dụng cho giải thưởng này'
        }
      });
    }

    const productInstance = instanceRows[0];
    console.log("productInstance", productInstance);

    // Cập nhật trạng thái product_instance thành USED và gán user_id, đảm bảo chỉ cập nhật khi status vẫn là AVAILABLE
    const [updateInstanceResult] = await connection.execute(
      'UPDATE product_instances SET status = "USED", user_id = ?, updated_at = NOW() WHERE id = ? AND status = "AVAILABLE"',
      [userId, productInstance.id]
    );
    
    // Kiểm tra xem việc cập nhật có thành công không
    if (updateInstanceResult.affectedRows === 0) {
      console.log("INSTANCE_ALREADY_USED");
      await connection.rollback();
      return res.status(400).json({
        error: {
          code: 'INSTANCE_ALREADY_USED',
          message: 'Sản phẩm này đã được sử dụng bởi người dùng khác'
        }
      });
    }

    // Tạo lịch sử quay có trúng thưởng
    const [historyResult] = await connection.execute(
      'INSERT INTO spin_history (user_id, campaign_id, campaign_prize_id, product_instance_id, actual_product_id, campaign_prize_group_id) VALUES (?, ?, ?, ?, ?, ?)',
      [userId, campaignId, selectedPrize.id, productInstance.id, actualProductId, campaignPrizeGroupId]
    );

    console.log("historyResult", historyResult);

    // Commit transaction
    await connection.commit();

    console.log(`✅ User ${userId} trúng giải: ${finalProductName} - Mã: ${productInstance.instance_code}`);

    // Lấy thông tin chi tiết của sản phẩm trúng thực tế (actualProductId) để hiển thị
    const [actualProductDetails] = await connection.execute(
      'SELECT product_name, product_image, product_type FROM products WHERE id = ?',
      [actualProductId]
    );

    const actualProductInfo = actualProductDetails[0] || {
      product_name: finalProductName,
      product_image: '/images/prize/default.png',
      product_type: 'VOUCHER' // Giá trị mặc định nếu không tìm thấy sản phẩm
    };

    res.status(200).json({
      product_id: returnProductId, // ID của nhóm (nếu có) hoặc sản phẩm
      actual_product_id: actualProductId, // ID của sản phẩm trúng thực tế
      instance_code: productInstance.instance_code,
      product_name: actualProductInfo.product_name, // Tên của sản phẩm trúng
      product_image: actualProductInfo.product_image, // Ảnh của sản phẩm trúng
      product_type: actualProductInfo.product_type // Loại sản phẩm (VOUCHER, PHYSICAL_GIFT, GROUP_GIFT)
    });

  } catch (error) {
    // Rollback transaction nếu có lỗi
    if (connection) {
      try {
        await connection.rollback();
      } catch (rollbackError) {
        console.error('Lỗi khi rollback transaction:', rollbackError);
      }
    }

    console.error(`❌ Lỗi xử lý spin:`, error.message);
    next(error);
  } finally {
    // Giải phóng connection
    if (connection) {
      connection.release();
    }
  }
};


// POST /v1/users/me/spins - Cộng lượt quay cho người dùng
export const incrementSpinCount = async (req, res, next) => {
  try {
    const { type } = req.body;
    const userId = req.user.id;

    // Kiểm tra type hợp lệ
    if (type !== 'facebook') {
      return res.status(400).json({
        error: {
          code: 'INVALID_TYPE',
          message: 'Loại phải là facebook'
        }
      });
    }

    // Kiểm tra đã share Facebook chưa với khóa FOR UPDATE
    const [userRows] = await pool.execute(
      'SELECT has_shared_facebook FROM users WHERE id = ? FOR UPDATE',
      [userId]
    );

    if (userRows.length === 0 || userRows[0].has_shared_facebook) {
      return res.status(400).json({
        error: {
          code: 'ALREADY_RECEIVED',
          message: 'Đã nhận thưởng chia sẻ Facebook'
        }
      });
    }

    const spinsToAdd = FACEBOOK_SHARE_SPIN_AWARD;
    const reason = 'FACEBOOK_SHARE';

    // Cập nhật database
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Cập nhật total_spins
      await connection.execute(
        'UPDATE users SET total_spins = total_spins + ? WHERE id = ?',
        [spinsToAdd, userId]
      );

      // Cập nhật flag Facebook share, đảm bảo chỉ cập nhật khi chưa share
      const [updateFacebookResult] = await connection.execute(
        'UPDATE users SET has_shared_facebook = TRUE WHERE id = ? AND has_shared_facebook = FALSE',
        [userId]
      );
      
      // Kiểm tra xem việc cập nhật có thành công không
      if (updateFacebookResult.affectedRows === 0) {
        await connection.rollback();
        return res.status(400).json({
          error: {
            code: 'ALREADY_RECEIVED',
            message: 'Đã nhận thưởng chia sẻ Facebook'
          }
        });
      }

      // Ghi log
      await connection.execute(
        'INSERT INTO spin_award_logs (user_id, spins_awarded, reason, award_source) VALUES (?, ?, ?, ?)',
        [userId, spinsToAdd, 'facebook bonus', reason]
      );

      await connection.commit();

      // Lấy tổng số lượt quay hiện tại
      const [updatedUserRows] = await pool.execute(
        'SELECT total_spins FROM users WHERE id = ?',
        [userId]
      );

      res.status(201).json({
        message: 'Cộng lượt quay thành công',
        total_spin: updatedUserRows[0].total_spins
      });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (err) {
    next(err);
  }
};