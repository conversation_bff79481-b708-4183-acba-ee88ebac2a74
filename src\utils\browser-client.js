/**
 * <PERSON><PERSON> dụ sử dụng CryptoJS trong trình duyệt để tạo checksum
 * Đ<PERSON><PERSON> là mã JavaScript có thể chạy trên trình duyệt web
 * 
 * Yêu cầu: Thêm thư viện CryptoJS vào trang web của bạn
 * <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
 */

// Hàm tạo checksum cho request
function generateChecksum(method, path, queryParams, body, timestamp, nonce, secretKey) {
  // Bắt đầu với timestamp và nonce
  let data = `timestamp=${timestamp}&nonce=${nonce}&path=${path}`;
  
  // Thêm query params nếu có
  if (queryParams && Object.keys(queryParams).length > 0) {
    const params = new URLSearchParams();
    
    // Sắp xếp keys theo thứ tự a-z
    const sortedKeys = Object.keys(queryParams).sort();
    
    for (const key of sortedKeys) {
      if (queryParams[key] !== undefined && queryParams[key] !== null) {
        params.append(key, queryParams[key]);
      }
    }
    
    const queryString = params.toString();
    if (queryString) {
      data += `&${queryString}`;
    }
  }
  
  // Thêm body cho các phương thức POST, PUT, PATCH
  if (['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) && body) {
    const bodyParams = new URLSearchParams();
    
    // Sắp xếp keys theo thứ tự a-z
    const sortedKeys = Object.keys(body).sort();
    
    for (const key of sortedKeys) {
      if (body[key] !== undefined && body[key] !== null) {
        // Chuyển đổi giá trị thành chuỗi
        const value = typeof body[key] === 'object' 
          ? JSON.stringify(body[key]) 
          : body[key].toString();
        
        bodyParams.append(key, value);
      }
    }
    
    const bodyString = bodyParams.toString();
    if (bodyString) {
      data += `&${bodyString}`;
    }
  }
  
  // Tính toán HMAC SHA-256 sử dụng CryptoJS
  const hmac = CryptoJS.HmacSHA256(data, secretKey);
  return hmac.toString(CryptoJS.enc.Hex);
}

// Hàm tạo headers cho request
function generateSecurityHeaders(method, path, queryParams, body, secretKey) {
  const timestamp = Date.now(); // Unix timestamp milliseconds
  const nonce = `${timestamp}-${Math.random().toString(36).substring(2, 15)}`; // Tạo nonce ngẫu nhiên
  
  const checksum = generateChecksum(method, path, queryParams, body, timestamp, nonce, secretKey);
  
  return {
    'x-timestamp': timestamp.toString(),
    'x-nonce': nonce,
    'x-checksum': checksum
  };
}

// Ví dụ sử dụng với Fetch API
async function fetchWithChecksum(url, options = {}, secretKey) {
  const method = options.method || 'GET';
  
  // Tách path và query params từ URL
  const urlObj = new URL(url);
  const path = urlObj.pathname;
  
  // Chuyển đổi query params thành object
  const queryParams = {};
  for (const [key, value] of urlObj.searchParams.entries()) {
    queryParams[key] = value;
  }
  
  // Tạo headers bảo mật
  const securityHeaders = generateSecurityHeaders(method, path, queryParams, options.body, secretKey);
  
  // Kết hợp headers
  const headers = {
    'Content-Type': 'application/json',
    ...securityHeaders,
    ...options.headers
  };
  
  // Tạo options mới với headers đã cập nhật
  const newOptions = {
    ...options,
    headers
  };
  
  // Thực hiện request
  try {
    const response = await fetch(url, newOptions);
    return await response.json();
  } catch (error) {
    console.error('Request failed:', error);
    throw error;
  }
}

// Ví dụ sử dụng với Axios
async function axiosWithChecksum(config, secretKey) {
  const method = config.method || 'GET';
  const url = config.url;
  
  // Tách path và query params từ URL
  const urlObj = new URL(url);
  const path = urlObj.pathname;
  
  // Chuyển đổi query params thành object
  const queryParams = config.params || {};
  
  // Tạo headers bảo mật
  const securityHeaders = generateSecurityHeaders(method, path, queryParams, config.data, secretKey);
  
  // Kết hợp headers
  const headers = {
    'Content-Type': 'application/json',
    ...securityHeaders,
    ...config.headers
  };
  
  // Tạo config mới với headers đã cập nhật
  const newConfig = {
    ...config,
    headers
  };
  
  // Thực hiện request
  try {
    const response = await axios(newConfig);
    return response.data;
  } catch (error) {
    console.error('Request failed:', error.response ? error.response.data : error.message);
    throw error;
  }
}

// Ví dụ sử dụng
// Lưu ý: Trong thực tế, secretKey phải được bảo mật và chỉ được sử dụng ở phía server
// Đây chỉ là ví dụ minh họa
const API_SECRET_KEY = 'your-secret-key-change-in-production';

// Ví dụ sử dụng với Fetch API
async function exampleFetch() {
  try {
    // GET request
    const getData = await fetchWithChecksum(
      'https://api.example.com/secure-data',
      { method: 'GET' },
      API_SECRET_KEY
    );
    console.log('GET Response:', getData);
    
    // POST request
    const postData = await fetchWithChecksum(
      'https://api.example.com/secure-action',
      {
        method: 'POST',
        body: JSON.stringify({ name: 'test', value: 123 })
      },
      API_SECRET_KEY
    );
    console.log('POST Response:', postData);
  } catch (error) {
    console.error('Error:', error);
  }
}

// Ví dụ sử dụng với Axios
async function exampleAxios() {
  try {
    // GET request
    const getData = await axiosWithChecksum(
      {
        method: 'GET',
        url: 'https://api.example.com/secure-data'
      },
      API_SECRET_KEY
    );
    console.log('GET Response:', getData);
    
    // POST request
    const postData = await axiosWithChecksum(
      {
        method: 'POST',
        url: 'https://api.example.com/secure-action',
        data: { name: 'test', value: 123 }
      },
      API_SECRET_KEY
    );
    console.log('POST Response:', postData);
  } catch (error) {
    console.error('Error:', error);
  }
}

// Gọi các hàm ví dụ
// exampleFetch();
// exampleAxios();