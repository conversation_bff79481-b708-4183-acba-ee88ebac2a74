import axios from 'axios';
import { ApiError, BadRequestError, InternalServerError } from '../errors/index.js';
import { CLOUDFLARE_TURNSTILE_SECRET_KEY, NODE_ENV } from '../configs/env.js';

/**
 * Middleware xác thực Cloudflare Turnstile Captcha
 */
export const verifyCaptcha = async (req, res, next) => {
  try {
    let { captcha_token } = req.body;
    if (!captcha_token) captcha_token = req.query.captcha_token;

    // Kiểm tra captcha_token có tồn tại không
    if (!captcha_token) {
      throw new BadRequestError('CAPTCHA_REQUIRED', 'Captcha token là bắt buộc');
    }

    // Lấy secret key từ config
    if (!CLOUDFLARE_TURNSTILE_SECRET_KEY) {
      console.error('CLOUDFLARE_TURNSTILE_SECRET_KEY not configured');
      throw new InternalServerError('CAPTCHA_CONFIG_ERROR', '<PERSON><PERSON><PERSON> hình captcha không hợp lệ');
    }

    // <PERSON><PERSON><PERSON> request đến Cloudflare để verify captcha
    const verifyUrl = 'https://challenges.cloudflare.com/turnstile/v0/siteverify';
    const formData = new URLSearchParams();
    formData.append('secret', CLOUDFLARE_TURNSTILE_SECRET_KEY);
    formData.append('response', captcha_token);
    formData.append('remoteip', req.ip);

    const response = await axios.post(verifyUrl, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      timeout: 10000 // 10 seconds timeout
    });

    const result = response.data;

    // Kiểm tra kết quả verify
    if (!result.success) {
      console.error('Captcha verification failed:', result);
      throw new BadRequestError('CAPTCHA_INVALID', 'Captcha không hợp lệ');
    }

    // Captcha hợp lệ, tiếp tục xử lý
    next();
  } catch (error) {
    if (error instanceof ApiError) {
      next(error);
    } else {
      console.error('Captcha verification error:', error);
      next(new InternalServerError('CAPTCHA_VERIFY_ERROR', 'Lỗi xác thực captcha'));
    }
  }
};

/**
 * Middleware xác thực captcha cho development (bypass)
 * Chỉ sử dụng khi NODE_ENV !== 'production'
 */
export const verifyCaptchaDev = (req, res, next) => {
  if (NODE_ENV === 'production') {
    return verifyCaptcha(req, res, next);
  }

  // Bypass captcha trong development
  console.log('Development mode: Bypassing captcha verification');
  next();
};