import { pool } from '../configs/db.js';

/**
 * Middleware làm mới thông tin user trong session từ database
 * Đ<PERSON><PERSON> bảo thông tin trong session luôn cập nhật
 */
export const refreshUserSession = async (req, res, next) => {
  try {
    // Chỉ làm mới nếu có user trong session
    if (req.session && req.session.user && req.session.user.id) {
      const userId = req.session.user.id;
      
      // Lấy thông tin user mới nhất từ database
      const [users] = await pool.query(
        'SELECT id, phone_number, full_name, total_spins, used_spins FROM users WHERE id = ? AND deleted_at IS NULL',
        [userId]
      );
      
      if (users.length > 0) {
        const user = users[0];
        // Cập nhật thông tin user trong session
        req.session.user = {
          id: user.id,
          phone_number: user.phone_number,
          full_name: user.full_name,
          total_spins: user.total_spins,
          used_spins: user.used_spins
        };
      } else {
        // User không tồn tại, xóa session
        req.session.destroy();
        return res.status(401).json({
          error: {
            code: 'USER_NOT_FOUND',
            message: 'Người dùng không tồn tại. Vui lòng đăng nhập lại.'
          }
        });
      }
    }
    
    next();
  } catch (error) {
    console.error('Session refresh error:', error);
    // Không throw error để không ảnh hưởng đến request
    next();
  }
};

/**
 * Middleware kiểm tra thời gian session
 * Tự động gia hạn session nếu user còn hoạt động
 */
export const checkSessionExpiry = (req, res, next) => {
  try {
    if (req.session && req.session.user && req.session.loginTime) {
      const now = Date.now();
      const sessionAge = now - req.session.loginTime;
      const maxAge = req.session.cookie.maxAge || (24 * 60 * 60 * 1000); // 24 hours default
      
      // Nếu session gần hết hạn (còn 2 giờ), gia hạn thêm
      if (sessionAge > (maxAge - 2 * 60 * 60 * 1000)) {
        req.session.loginTime = now;
        console.log(`Session extended for user ${req.session.user.id}`);
      }
    }
    
    next();
  } catch (error) {
    console.error('Session expiry check error:', error);
    next();
  }
};

/**
 * Middleware xóa session khi logout
 */
export const clearSession = (req, res, next) => {
  if (req.session) {
    req.session.destroy((err) => {
      if (err) {
        console.error('Session destroy error:', err);
      }
      next();
    });
  } else {
    next();
  }
};