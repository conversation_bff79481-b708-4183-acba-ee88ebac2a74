/**
 * Base API Error class
 * Xử lý các lỗi API với mã HTTP, mã lỗi và thông báo
 */
class ApiError extends Error {
  constructor(httpCode, errorCode, message, details = null) {
    super(message);
    this.name = 'ApiError';
    this.httpCode = httpCode;
    this.statusCode = httpCode; // Thêm statusCode để phù hợp với log model
    this.errorCode = errorCode;
    this.details = details;
    
    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Chuyển đổi thành object JSON để response
   */
  toJSON() {
    const result = {
      error: {
        code: this.errorCode,
        message: this.message
      }
    };

    if (this.details) {
      result.error.details = this.details;
    }

    return result;
  }
}

export default ApiError;