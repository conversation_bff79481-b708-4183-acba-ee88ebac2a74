import { pool } from "../configs/db.js";

export const getWinners = async (req, res, next) => {
  try {
    // L<PERSON>y tham số phân trang từ query params
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const connection = await pool.getConnection();

    try {
      // Đếm tổng số người trúng thưởng
      const [countResult] = await connection.query(`
        SELECT COUNT(DISTINCT sh.id) as total 
        FROM spin_history sh
        INNER JOIN users u ON sh.user_id = u.id
        INNER JOIN product_instances pi ON sh.product_instance_id = pi.id
        WHERE sh.product_instance_id IS NOT NULL AND u.deleted_at IS NULL
      `);
      const total = countResult[0].total;
      const totalPages = Math.ceil(total / limit);

      // L<PERSON>y danh sách người trúng thưởng với phân trang
      const [rows] = await connection.query(`
        SELECT 
          CONCAT(LEFT(u.phone_number, 3), 'xxx', RIGHT(u.phone_number, 3)) as phone_number,
          u.full_name as full_name,
          COALESCE(actual_p.product_name, p.product_name) as name,
          COALESCE(actual_p.product_image, p.product_image) as image,
          sh.spun_at as spun_at
        FROM spin_history sh
        INNER JOIN users u ON sh.user_id = u.id
        INNER JOIN product_instances pi ON sh.product_instance_id = pi.id
        INNER JOIN products p ON pi.product_id = p.id
        LEFT JOIN products actual_p ON sh.actual_product_id = actual_p.id
        WHERE sh.product_instance_id IS NOT NULL AND u.deleted_at IS NULL
        ORDER BY sh.spun_at DESC
        LIMIT ? OFFSET ?
      `, [limit, offset]);

      // Định dạng kết quả theo API spec
      const formattedData = rows.map(row => ({
        phone_number: row.phone_number,
        full_name: row.full_name,
        product: {
          name: row.name,
          image: row.image
        },
        spun_at: row.spun_at
      }));

      // Trả về kết quả với định dạng theo API spec
      res.json({
        data: formattedData,
        pagination: {
          total: total,
          page: page,
          limit: limit,
          total_pages: totalPages
        }
      });
    } finally {
      connection.release();
    }
  } catch (err) {
    next(err);
  }
}