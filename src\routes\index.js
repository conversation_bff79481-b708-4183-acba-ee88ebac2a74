import express from 'express';
import { login, callback, logout, getPrizes, getDetailedPrizes, getWinners, getRewards, spin, incrementSpinCount, getMe, getSystemStatus } from "../controllers/index.js"

import { requireAuth, optionalAuth } from "../middlewares/auth.js"
import { refreshUserSession, checkSessionExpiry, clearSession } from "../middlewares/session.js"
import { verifyChecksum } from "../middlewares/checksum.js"
import { defaultRateLimiter, loginRateLimiter, cacheMiddleware, clearCache } from "../middlewares/index.js"
import { API_SECRET_KEY } from "../configs/env.js"

const router = express.Router();


router.get("/callback", defaultRateLimiter, callback)
// Auth routes
router.use(verifyChecksum(API_SECRET_KEY))
// Áp dụng rate limit riêng cho route login
router.get("/auth/login", loginRateLimiter, login)

router.post("/auth/logout", clearSession, logout)

// Endpoints yêu cầu xác thực với session management
router.post("/v1/spins", checkSessionExpiry, refreshUserSession, requireAuth, defaultRateLimiter, clearCache("cache:/v1/prizes*"), clearCache("cache:/v1/winners*"), spin)
router.get("/v1/users/me", checkSessionExpiry, refreshUserSession, requireAuth, defaultRateLimiter, getMe)
router.get("/v1/users/me/rewards", checkSessionExpiry, refreshUserSession, requireAuth, defaultRateLimiter, getRewards)
router.post("/v1/users/me/spins", checkSessionExpiry, refreshUserSession, requireAuth, defaultRateLimiter, clearCache("cache:/v1/prizes*"), incrementSpinCount)

// Endpoints không yêu cầu xác thực nhưng có thể có session
// Áp dụng cache 1 phút (60 giây) cho các API lấy dữ liệu
router.get("/v1/prizes", optionalAuth, cacheMiddleware(60), getPrizes)
router.get("/v1/prizes/detailed", optionalAuth, cacheMiddleware(60), defaultRateLimiter, getDetailedPrizes)
router.get("/v1/winners", optionalAuth, cacheMiddleware(60), getWinners)

// System status endpoint - không cache và không yêu cầu xác thực
// router.get("/v1/system/status", getSystemStatus)

export default router;