import { ApiError, UnauthorizedError, InternalServerError } from '../errors/index.js';

/**
 * Middleware yêu cầu xác thực người dùng
 * Chỉ sử dụng session-based authentication từ OIDC
 */
export const requireAuth = (req, res, next) => {
  try {
    // Kiểm tra session-based auth
    if (req.session && req.session.user) {
      // <PERSON>án thông tin user từ session
      req.user = req.session.user;
      return next();
    }

    // Không có session hợp lệ
    throw new UnauthorizedError('NO_SESSION', 'Không có phiên đăng nhập hợp lệ. Vui lòng đăng nhập lại.');
    
  } catch (error) {
    if (error instanceof ApiError) {
      return next(error);
    }

    return next(new InternalServerError('AUTH_ERROR', 'Lỗi xác thực'));
  }
};

/**
 * Middleware kiểm tra session tùy chọn (không b<PERSON>t buộc đăng nhập)
 */
export const optionalAuth = (req, res, next) => {
  try {
    // Kiểm tra session-based auth
    if (req.session && req.session.user) {
      // Gán thông tin user từ session
      req.user = req.session.user;
    }
    // Không có session thì vẫn cho phép tiếp tục
    next();
  } catch (error) {
    // Có lỗi thì vẫn cho phép tiếp tục nhưng không có user
    next();
  }
};