import express from 'express';
const app = express();
const port = 5080;

app.use(express.json());

const fullNames = ["Nguyễn <PERSON>", "Trần T<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>àng Q<PERSON>ốc Đạ<PERSON>"];
const contractPrefixes = ["BKAAA", "HNAAB", "SGAAA", "DNAAC"];
const productNames = ["FCAM Play 4", "FCAM IQ4S", "FCAM Play 3", "FCAM Pro 5", "FCAM Lite"];
const phonePrefixes = ["090", "091", "093", "094", "097"];

const generateRandomString = (length) => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
};

const generateRandomPhone = () => {
    const prefix = phonePrefixes[Math.floor(Math.random() * phonePrefixes.length)];
    return `${prefix}***${Math.floor(1000 + Math.random() * 9000)}`;
};

const generateRandomProducts = () => {
    const productCount = Math.floor(Math.random() * 3) + 1; // 1-3 sản phẩm
    const products = [];
    const usedProductNames = new Set();

    while (products.length < productCount) {
        const productName = productNames[Math.floor(Math.random() * productNames.length)];
        if (!usedProductNames.has(productName)) {
            usedProductNames.add(productName);
            products.push({
                Name: productName,
                Qty: Math.floor(Math.random() * 3) + 1 // Số lượng 1-3
            });
        }
    }
    return products;
};

const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token || token !== 'fake-token-1234567890') {
        return res.status(401).json({
            Code: 401,
            Message: {
                Message: "Không được phép truy cập",
                ExMessage: "Token không tồn tại"
            }
        });
    }
    next();
};

app.post('/ftel-gw/selfservice/game/api/v1/auth/token', (req, res) => {
    // handle basic authen
    const authHeader = req.headers['authorization'];
    console.log('authHeader', authHeader);
    if (!authHeader || !authHeader.startsWith('Basic ')) {
        return res.status(401).json({
            Code: 401,
            Message: {
                Message: "Không được phép truy cập",
                ExMessage: "Thiếu thông tin xác thực Basic Auth"
            }
        });
    }

    // Giải mã Base64 từ Basic Auth
    const base64Credentials = authHeader.split(' ')[1];
    const credentials = Buffer.from(base64Credentials, 'base64').toString('utf8');
    const [username, password] = credentials.split(':');

    // Kiểm tra username và password
    if (username !== 'admin' || password !== 'admin') {
        return res.status(401).json({
            Code: 401,
            Message: {
                Message: "Không được phép truy cập",
                ExMessage: "Thông tin xác thực không hợp lệ"
            }
        });
    }
    try {
        res.json({
            Code: 200,
            Message: {
                Message: "Thành công.",
                ExMessage: null
            },
            Data: {
                AccessToken: "fake-token-1234567890"
            }
        });
    } catch (error) {
        res.status(500).json({
            Code: 500,
            Message: {
                Message: "Lỗi hệ thống",
                ExMessage: error.message
            }
        });
    }
});

app.post('/ftel-gw/selfservice/game/api/v1/game/get-contract-by-phone', authenticateToken, (req, res) => {
    const { Phone, GameKey } = req.body;

    if (!Phone || !GameKey) {
        return res.status(400).json({
            Code: 400,
            Message: {
                Message: "Thiếu tham số",
                ExMessage: "Phone và GameKey là bắt buộc"
            }
        });
    }

    const orderCount = Math.floor(Math.random() * 3) + 1;
    const orders = [];

    for (let i = 0; i < orderCount; i++) {
        const contractPrefix = contractPrefixes[Math.floor(Math.random() * contractPrefixes.length)];
        orders.push({
            Contract: `${contractPrefix}${generateRandomString(4)}`,
            OrderCode: `BKN${generateRandomString(8)}`,
            FullName: fullNames[Math.floor(Math.random() * fullNames.length)],
            Phone: generateRandomPhone(),
            Products: generateRandomProducts(),
            Services: null
        });
    }

    const fakeResponse = {
        Code: 200,
        Message: {
            Message: "Thành công",
            ExMessage: null
        },
        Data: {
            Order: orders
        }
    };

    res.json(fakeResponse);
});

app.listen(port, () => {
    console.log(`Server is running on port ${port}`);
});