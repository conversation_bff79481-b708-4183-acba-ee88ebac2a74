import axios from 'axios';

import { FTEL_API_AUTH_ENDPOINT, FTEL_API_CLIENT_ID, FTEL_API_CLIENT_SECRET, FTEL_API_GAME_ID, FTEL_API_ENDPOINT } from '../configs/env.js';
// Biến global để lưu token
let globalAccessToken = null;

// Hàm lấy token từ API xác thực
export const getAuthToken = async () => {
    const apiAuthEndpoint = FTEL_API_AUTH_ENDPOINT || 'http://localhost:5080/ftel-gw/selfservice/game/api/v1/auth/token';
    const username = FTEL_API_CLIENT_ID || 'admin';
    const password = FTEL_API_CLIENT_SECRET || 'admin';
    console.log('apiAuthEndpoint:', apiAuthEndpoint);
    console.log('username:', username);
    console.log('password:', password);
    if (!apiAuthEndpoint) {
        console.log('Missing authentication endpoint configuration');
        return null;
    }

    try {
        // Tạo Basic Auth header
        const basicAuth = Buffer.from(`${username}:${password}`).toString('base64');
        console.log('basicAuth', basicAuth);
        const config = {
            method: 'post',
            url: apiAuthEndpoint,
            headers: {
                'Authorization': `Basic ${basicAuth}`,
                'Content-Type': 'application/json'
            },
            data: {
                "GrantType": "client_credentials"
            }
        };

        const response = await axios(config);
        console.log('response', JSON.stringify(response.data));
        if (response.data && response.data.Code === 200 && response.data.Data && response.data.Data.AccessToken) {
            console.log('Authentication successful, token received');
            return response.data.Data.AccessToken;
        } else {
            console.error('Authentication failed:', response.data);
            return null;
        }
    } catch (error) {
        if (error.response) {
            console.log('response', JSON.stringify(error.response.data));
        }
        console.error('Authentication API call failed:', error.message);
        return null;
    }
};

export const getContractByPhone = async (phone_number) => {
    const apiEndpoint = FTEL_API_ENDPOINT || 'http://localhost:5080/ftel-gw/selfservice/game/api/v1/game/get-contract-by-phone';
    const gameId = FTEL_API_GAME_ID || "21d984a4-8a98-47a0-8bcb-dcae2ffc9820";

    // Kiểm tra và lấy token nếu chưa có
    if (!globalAccessToken) {
        globalAccessToken = await getAuthToken();
        if (!globalAccessToken) {
            console.error('Failed to get authentication token');
            throw new Error('Failed to authenticate');
        }
    }

    try {
        // Call API thực tế
        const data = JSON.stringify({
            "Phone": phone_number,
            "GameKey": gameId
        });

        const config = {
            method: 'post',
            url: apiEndpoint,
            headers: {
                'Authorization': `Bearer ${globalAccessToken}`,
                'Content-Type': 'application/json'
            },
            data: data
        };

        const response = await axios(config);
        console.log('API call successful:', JSON.stringify(response.data));
        return response.data;
    } catch (error) {
        if (error.response) {
            console.log('response', JSON.stringify(error.response.data));
        }
        console.error('API call failed:', error.message);

        // Kiểm tra nếu lỗi là do token hết hạn hoặc không hợp lệ (401)
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
            console.log('Token expired or invalid, trying to get a new token');
            // Lấy token mới
            globalAccessToken = await getAuthToken();

            if (globalAccessToken) {
                // Thử lại request với token mới
                try {
                    const retryConfig = {
                        method: 'post',
                        url: apiEndpoint,
                        headers: {
                            'Authorization': `Bearer ${globalAccessToken}`,
                            'Content-Type': 'application/json'
                        },
                        data: JSON.stringify({
                            "Phone": phone_number,
                            "GameKey": gameId
                        })
                    };

                    const retryResponse = await axios(retryConfig);
                    console.log('Retry API call successful:', JSON.stringify(retryResponse.data));
                    return retryResponse.data;
                } catch (retryError) {
                    console.error('Retry API call failed:', retryError.message);
                    throw retryError;
                }
            } else {
                throw new Error('Failed to refresh authentication token');
            }
        }

        // Nếu không phải lỗi token, ném lỗi để xử lý ở nơi gọi hàm
        throw error;
    }
}

