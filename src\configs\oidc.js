import * as client from 'openid-client';
import { OIDC_CLIENT_ID, OIDC_REDIRECT_URI, OIDC_ISSUER_URL, FRONTEND_REDIRECT_URL } from './env.js';
import * as undici from 'undici';

const OIDC_CONFIG = {
    CLIENT_ID: OIDC_CLIENT_ID,
    REDIRECT_URI: OIDC_REDIRECT_URI,
    ISSUER_URL: OIDC_ISSUER_URL,
    SCOPE: 'openid profile email',
    FRONTEND_REDIRECT_URL: FRONTEND_REDIRECT_URL
};

let oidcConfig;

const initializeOIDC = async () => {
    try {
        console.log(process.env.HTTP_PROXY)
        console.log(process.env.HTTPS_PROXY)
        let envHttpProxyAgent = new undici.EnvHttpProxyAgent({ httpProxy: process.env.HTTP_PROXY, httpsProxy: process.env.HTTP_PROXY, noProxy: process.env.NO_PROXY || "saleplatform-api-stag.fpt.net" });

        oidcConfig = await client.discovery(
            new URL(OIDC_CONFIG.ISSUER_URL),
            OIDC_CONFIG.CLIENT_ID,
            undefined,
            undefined,
            {
                [client.customFetch]: (...args) => {
                    return undici.fetch(args[0], { ...args[1], dispatcher: envHttpProxyAgent })
                },
                algorithm: 'oidc',
                timeout: 30
            }
        );

        console.log('OIDC Client initialized successfully');
        console.log('Discovered FPT ID endpoints:', oidcConfig.serverMetadata());
        return oidcConfig;
    } catch (error) {
        console.error('OIDC Initialization Error:', error);
        throw error;
    }
};
// Getter cho config
const getOIDCConfig = () => {
    if (!oidcConfig) {
        throw new Error('OIDC client not initialized. Call initializeOIDC() first.');
    }
    return oidcConfig;
};

export { OIDC_CONFIG, initializeOIDC, getOIDCConfig };